/**
 * SizeWise Suite - Project Properties Panel
 * 
 * Retractable panel (top-left) with 5 sections as specified in requirements:
 * 1. Info - Project details
 * 2. Codes - Building codes and standards
 * 3. Defaults - Default values and settings
 * 4. Team - Team members and permissions
 * 5. Admin - Administrative settings
 */

'use client';

import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { GlassCard } from '@/shared/components/glassmorphism/GlassCard';

interface ProjectPropertiesPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

type PanelSection = 'info' | 'codes' | 'defaults' | 'team' | 'admin';

export const ProjectPropertiesPanel: React.FC<ProjectPropertiesPanelProps> = ({
  isOpen,
  onToggle,
  className = '',
}) => {
  const [activeSection, setActiveSection] = useState<PanelSection>('info');

  const sections = [
    { id: 'info' as PanelSection, label: 'Info', icon: '📋' },
    { id: 'codes' as PanelSection, label: 'Codes', icon: '📖' },
    { id: 'defaults' as PanelSection, label: 'Defaults', icon: '⚙️' },
    { id: 'team' as PanelSection, label: 'Team', icon: '👥' },
    { id: 'admin' as PanelSection, label: 'Admin', icon: '🔧' },
  ];

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'info':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Project Name
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                placeholder="Enter project name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                rows={3}
                placeholder="Project description"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Location
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                placeholder="Project location"
              />
            </div>
          </div>
        );
      
      case 'codes':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Building Code
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50">
                <option>IBC 2021</option>
                <option>IBC 2018</option>
                <option>IBC 2015</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                ASHRAE Standard
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50">
                <option>ASHRAE 90.1-2019</option>
                <option>ASHRAE 90.1-2016</option>
                <option>ASHRAE 62.1-2019</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                SMACNA Standards
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">HVAC Duct Construction Standards</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">Duct Design Guidelines</span>
                </label>
              </div>
            </div>
          </div>
        );
      
      case 'defaults':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Default Friction Rate
              </label>
              <input
                type="number"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                placeholder="0.10"
              />
              <span className="text-xs text-gray-500">inches of water per 100 feet</span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Default Duct Material
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50">
                <option>Galvanized Steel</option>
                <option>Stainless Steel</option>
                <option>Aluminum</option>
                <option>Fiberglass</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Default Velocity Limits
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <input
                    type="number"
                    className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                    placeholder="1000"
                  />
                  <span className="text-xs text-gray-500">Min FPM</span>
                </div>
                <div>
                  <input
                    type="number"
                    className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                    placeholder="1800"
                  />
                  <span className="text-xs text-gray-500">Max FPM</span>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'team':
        return (
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Team Members</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-white/30 dark:bg-gray-800/30 rounded">
                  <div>
                    <div className="font-medium text-sm">John Doe</div>
                    <div className="text-xs text-gray-500">Project Manager</div>
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Owner</span>
                </div>
                <div className="flex items-center justify-between p-2 bg-white/30 dark:bg-gray-800/30 rounded">
                  <div>
                    <div className="font-medium text-sm">Jane Smith</div>
                    <div className="text-xs text-gray-500">HVAC Engineer</div>
                  </div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Editor</span>
                </div>
              </div>
            </div>
            <button className="w-full px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
              Invite Team Member
            </button>
          </div>
        );
      
      case 'admin':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Project Visibility
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50">
                <option>Private</option>
                <option>Team Only</option>
                <option>Organization</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Backup Settings
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">Auto-save every 5 minutes</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">Cloud backup</span>
                </label>
              </div>
            </div>
            <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
              <button className="w-full px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors">
                Delete Project
              </button>
            </div>
          </div>
        );
      
      default:
        return <div>Section not implemented</div>;
    }
  };

  return (
    <div className={`flex ${className}`}>
      {/* Panel Content */}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isOpen ? 'w-80 opacity-100' : 'w-0 opacity-0'
        } overflow-hidden`}
      >
        <GlassCard className="h-full">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Project Properties
            </h3>
            
            {/* Section Tabs */}
            <div className="flex space-x-1 mb-4 bg-gray-100/50 dark:bg-gray-800/50 rounded-lg p-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`flex-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                    activeSection === section.id
                      ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                  }`}
                >
                  <div className="flex flex-col items-center">
                    <span className="text-sm mb-1">{section.icon}</span>
                    <span>{section.label}</span>
                  </div>
                </button>
              ))}
            </div>
            
            {/* Section Content */}
            <div className="max-h-96 overflow-y-auto">
              {renderSectionContent()}
            </div>
          </div>
        </GlassCard>
      </div>
      
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="ml-2 w-8 h-12 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-r-lg flex items-center justify-center hover:bg-white/90 dark:hover:bg-gray-800/90 transition-colors"
      >
        {isOpen ? (
          <ChevronLeftIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <ChevronRightIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        )}
      </button>
    </div>
  );
};
