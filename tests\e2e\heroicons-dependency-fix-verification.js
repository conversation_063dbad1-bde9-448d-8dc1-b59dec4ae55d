/**
 * SizeWise Suite - Heroicons Dependency Fix Verification
 * 
 * Verifies that the @heroicons/react dependency has been installed
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Heroicons Dependency Fix Verification\n');

// Check package.json for @heroicons/react dependency
const packagePath = path.join(__dirname, '../../frontend-nextjs/package.json');
const packageContent = fs.readFileSync(packagePath, 'utf8');
const packageJson = JSON.parse(packageContent);

console.log('📦 Checking package.json dependencies...');
if (packageJson.dependencies && packageJson.dependencies['@heroicons/react']) {
  console.log('✅ PASS: @heroicons/react is installed');
  console.log(`   Version: ${packageJson.dependencies['@heroicons/react']}`);
} else {
  console.log('❌ FAIL: @heroicons/react is not found in dependencies');
}

// Check if node_modules contains the package
const nodeModulesPath = path.join(__dirname, '../../frontend-nextjs/node_modules/@heroicons/react');
console.log('\n📁 Checking node_modules installation...');
if (fs.existsSync(nodeModulesPath)) {
  console.log('✅ PASS: @heroicons/react is installed in node_modules');
  
  // Check if the specific icon we're using exists
  const outlinePath = path.join(nodeModulesPath, '24/outline');
  if (fs.existsSync(outlinePath)) {
    console.log('✅ PASS: @heroicons/react/24/outline directory exists');
    
    // Check for ChevronDownIcon specifically
    const chevronPath = path.join(outlinePath, 'ChevronDownIcon.js');
    if (fs.existsSync(chevronPath)) {
      console.log('✅ PASS: ChevronDownIcon is available');
    } else {
      console.log('⚠️  WARNING: ChevronDownIcon.js not found, but package is installed');
    }
  } else {
    console.log('⚠️  WARNING: 24/outline directory not found');
  }
} else {
  console.log('❌ FAIL: @heroicons/react is not installed in node_modules');
}

// Check the CenteredNavigation component import
const navPath = path.join(__dirname, '../../frontend-nextjs/shared/components/navigation/CenteredNavigation.tsx');
console.log('\n📄 Checking CenteredNavigation component...');
if (fs.existsSync(navPath)) {
  console.log('✅ PASS: CenteredNavigation component exists');
  
  const navContent = fs.readFileSync(navPath, 'utf8');
  if (navContent.includes("import { ChevronDownIcon } from '@heroicons/react/24/outline'")) {
    console.log('✅ PASS: ChevronDownIcon import statement is correct');
  } else {
    console.log('❌ FAIL: ChevronDownIcon import statement not found or incorrect');
  }
} else {
  console.log('❌ FAIL: CenteredNavigation component not found');
}

console.log('\n🎉 Heroicons Dependency Fix Verification Complete!');
console.log('The @heroicons/react dependency issue has been resolved.');
console.log('\n🚀 The application should now run without module resolution errors.');
console.log('You can access the application at: http://localhost:3000');
