/**
 * SizeWise Suite - SMACNA Standards Compliance Service
 * 
 * Versioned module for SMACNA (Sheet Metal and Air Conditioning 
 * Contractors' National Association) standards compliance
 * 
 * Required by Augment Implementation Protocol:
 * - Versioned module in /features/standards-compliance/services/
 * - Unit tested calculation validation
 * - No direct calculation bypass allowed
 */

import { z } from 'zod';

// Version information for standards compliance
export const SMACNA_VERSION = '2023.1.0';
export const LAST_UPDATED = '2025-01-20';

// Input validation schemas
const DuctSizingInputSchema = z.object({
  airflow: z.number().positive('Airflow must be positive'),
  frictionRate: z.number().positive('Friction rate must be positive'),
  ductType: z.enum(['rectangular', 'round']),
  material: z.string().min(1, 'Material must be specified'),
  velocity: z.number().optional(),
  maxVelocity: z.number().optional(),
});

const RectangularDuctSchema = z.object({
  width: z.number().positive('Width must be positive'),
  height: z.number().positive('Height must be positive'),
});

const RoundDuctSchema = z.object({
  diameter: z.number().positive('Diameter must be positive'),
});

// SMACNA standard duct sizes (inches)
export const SMACNA_RECTANGULAR_SIZES = [
  // Standard rectangular duct sizes
  { width: 6, height: 4 }, { width: 8, height: 4 }, { width: 10, height: 4 },
  { width: 12, height: 4 }, { width: 14, height: 4 }, { width: 16, height: 4 },
  { width: 8, height: 6 }, { width: 10, height: 6 }, { width: 12, height: 6 },
  { width: 14, height: 6 }, { width: 16, height: 6 }, { width: 18, height: 6 },
  { width: 20, height: 6 }, { width: 10, height: 8 }, { width: 12, height: 8 },
  { width: 14, height: 8 }, { width: 16, height: 8 }, { width: 18, height: 8 },
  { width: 20, height: 8 }, { width: 22, height: 8 }, { width: 24, height: 8 },
  // Add more standard sizes as needed
];

export const SMACNA_ROUND_SIZES = [
  4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30,
  32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60
];

// SMACNA velocity limits (FPM - Feet Per Minute)
export const SMACNA_VELOCITY_LIMITS = {
  supply: {
    main: { min: 1000, max: 1800, recommended: 1200 },
    branch: { min: 800, max: 1500, recommended: 1000 },
    riser: { min: 1000, max: 2000, recommended: 1400 },
  },
  return: {
    main: { min: 800, max: 1500, recommended: 1000 },
    branch: { min: 600, max: 1200, recommended: 800 },
    riser: { min: 800, max: 1600, recommended: 1200 },
  },
  exhaust: {
    general: { min: 1000, max: 2500, recommended: 1500 },
    kitchen: { min: 1500, max: 3000, recommended: 2000 },
    industrial: { min: 2000, max: 4000, recommended: 2500 },
  },
};

// Material properties for pressure loss calculations
export const SMACNA_MATERIAL_PROPERTIES = {
  'galvanized-steel': { roughness: 0.0003, density: 0.0765 },
  'stainless-steel': { roughness: 0.0002, density: 0.0765 },
  'aluminum': { roughness: 0.0002, density: 0.0275 },
  'fiberglass': { roughness: 0.003, density: 0.0125 },
  'pvc': { roughness: 0.0001, density: 0.0875 },
};

export interface DuctSizingInput {
  airflow: number; // CFM
  frictionRate: number; // inches of water per 100 feet
  ductType: 'rectangular' | 'round';
  material: keyof typeof SMACNA_MATERIAL_PROPERTIES;
  velocity?: number; // FPM
  maxVelocity?: number; // FPM
  application?: 'supply' | 'return' | 'exhaust';
  subType?: 'main' | 'branch' | 'riser' | 'general' | 'kitchen' | 'industrial';
}

export interface DuctSizingResult {
  // Calculated dimensions
  dimensions: {
    width?: number;
    height?: number;
    diameter?: number;
    area: number; // square inches
    perimeter: number; // inches
    hydraulicDiameter: number; // inches
  };
  
  // Performance metrics
  performance: {
    velocity: number; // FPM
    pressureLoss: number; // inches of water per 100 feet
    reynoldsNumber: number;
    frictionFactor: number;
  };
  
  // SMACNA compliance
  compliance: {
    isCompliant: boolean;
    velocityCompliant: boolean;
    sizeCompliant: boolean;
    warnings: string[];
    recommendations: string[];
  };
  
  // Metadata
  metadata: {
    standardsVersion: string;
    calculationMethod: string;
    timestamp: string;
  };
}

export class SMACNAService {
  private static instance: SMACNAService;
  
  public static getInstance(): SMACNAService {
    if (!SMACNAService.instance) {
      SMACNAService.instance = new SMACNAService();
    }
    return SMACNAService.instance;
  }

  /**
   * Calculate duct sizing according to SMACNA standards
   */
  public calculateDuctSizing(input: DuctSizingInput): DuctSizingResult {
    // Validate input
    const validatedInput = DuctSizingInputSchema.parse(input);
    
    if (validatedInput.ductType === 'rectangular') {
      return this.calculateRectangularDuct(validatedInput);
    } else {
      return this.calculateRoundDuct(validatedInput);
    }
  }

  private calculateRectangularDuct(input: DuctSizingInput): DuctSizingResult {
    const { airflow, frictionRate, material, application = 'supply', subType = 'main' } = input;
    
    // Calculate required area
    const velocityLimits = SMACNA_VELOCITY_LIMITS[application][subType];
    const targetVelocity = input.velocity || velocityLimits.recommended;
    const requiredArea = airflow / targetVelocity; // square feet
    const requiredAreaInches = requiredArea * 144; // square inches
    
    // Find best fitting standard size
    const bestSize = this.findBestRectangularSize(requiredAreaInches);
    
    // Calculate actual performance
    const area = bestSize.width * bestSize.height;
    const perimeter = 2 * (bestSize.width + bestSize.height);
    const hydraulicDiameter = (4 * area) / perimeter;
    const actualVelocity = (airflow * 144) / area; // FPM
    
    // Calculate pressure loss using Darcy-Weisbach equation
    const materialProps = SMACNA_MATERIAL_PROPERTIES[material];
    const reynoldsNumber = this.calculateReynoldsNumber(actualVelocity, hydraulicDiameter);
    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, materialProps.roughness, hydraulicDiameter);
    const pressureLoss = this.calculatePressureLoss(actualVelocity, hydraulicDiameter, frictionFactor);
    
    // Check compliance
    const compliance = this.checkCompliance(actualVelocity, bestSize, application, subType, pressureLoss, frictionRate);
    
    return {
      dimensions: {
        width: bestSize.width,
        height: bestSize.height,
        area,
        perimeter,
        hydraulicDiameter,
      },
      performance: {
        velocity: actualVelocity,
        pressureLoss,
        reynoldsNumber,
        frictionFactor,
      },
      compliance,
      metadata: {
        standardsVersion: SMACNA_VERSION,
        calculationMethod: 'SMACNA Rectangular Duct Sizing',
        timestamp: new Date().toISOString(),
      },
    };
  }

  private calculateRoundDuct(input: DuctSizingInput): DuctSizingResult {
    const { airflow, frictionRate, material, application = 'supply', subType = 'main' } = input;
    
    // Calculate required diameter
    const velocityLimits = SMACNA_VELOCITY_LIMITS[application][subType];
    const targetVelocity = input.velocity || velocityLimits.recommended;
    const requiredArea = airflow / targetVelocity; // square feet
    const requiredDiameter = Math.sqrt((requiredArea * 144 * 4) / Math.PI); // inches
    
    // Find best fitting standard size
    const bestDiameter = this.findBestRoundSize(requiredDiameter);
    
    // Calculate actual performance
    const area = (Math.PI * bestDiameter * bestDiameter) / 4;
    const perimeter = Math.PI * bestDiameter;
    const hydraulicDiameter = bestDiameter; // For round ducts, hydraulic diameter = diameter
    const actualVelocity = (airflow * 144) / area; // FPM
    
    // Calculate pressure loss
    const materialProps = SMACNA_MATERIAL_PROPERTIES[material];
    const reynoldsNumber = this.calculateReynoldsNumber(actualVelocity, hydraulicDiameter);
    const frictionFactor = this.calculateFrictionFactor(reynoldsNumber, materialProps.roughness, hydraulicDiameter);
    const pressureLoss = this.calculatePressureLoss(actualVelocity, hydraulicDiameter, frictionFactor);
    
    // Check compliance
    const compliance = this.checkCompliance(actualVelocity, { diameter: bestDiameter }, application, subType, pressureLoss, frictionRate);
    
    return {
      dimensions: {
        diameter: bestDiameter,
        area,
        perimeter,
        hydraulicDiameter,
      },
      performance: {
        velocity: actualVelocity,
        pressureLoss,
        reynoldsNumber,
        frictionFactor,
      },
      compliance,
      metadata: {
        standardsVersion: SMACNA_VERSION,
        calculationMethod: 'SMACNA Round Duct Sizing',
        timestamp: new Date().toISOString(),
      },
    };
  }

  private findBestRectangularSize(requiredArea: number): { width: number; height: number } {
    let bestSize = SMACNA_RECTANGULAR_SIZES[0];
    let bestAreaDiff = Math.abs(bestSize.width * bestSize.height - requiredArea);
    
    for (const size of SMACNA_RECTANGULAR_SIZES) {
      const area = size.width * size.height;
      const areaDiff = Math.abs(area - requiredArea);
      
      if (area >= requiredArea && areaDiff < bestAreaDiff) {
        bestSize = size;
        bestAreaDiff = areaDiff;
      }
    }
    
    return bestSize;
  }

  private findBestRoundSize(requiredDiameter: number): number {
    return SMACNA_ROUND_SIZES.find(size => size >= requiredDiameter) || SMACNA_ROUND_SIZES[SMACNA_ROUND_SIZES.length - 1];
  }

  private calculateReynoldsNumber(velocity: number, hydraulicDiameter: number): number {
    // Reynolds number calculation for air at standard conditions
    const kinematicViscosity = 1.57e-4; // ft²/s for air at 70°F
    const velocityFtPerSec = velocity / 60; // Convert FPM to ft/s
    const diameterFt = hydraulicDiameter / 12; // Convert inches to feet
    
    return (velocityFtPerSec * diameterFt) / kinematicViscosity;
  }

  private calculateFrictionFactor(reynoldsNumber: number, roughness: number, hydraulicDiameter: number): number {
    // Colebrook-White equation for friction factor
    const relativeRoughness = roughness / (hydraulicDiameter / 12);
    
    if (reynoldsNumber < 2300) {
      // Laminar flow
      return 64 / reynoldsNumber;
    } else {
      // Turbulent flow - simplified approximation
      return 0.25 / Math.pow(Math.log10(relativeRoughness / 3.7 + 5.74 / Math.pow(reynoldsNumber, 0.9)), 2);
    }
  }

  private calculatePressureLoss(velocity: number, hydraulicDiameter: number, frictionFactor: number): number {
    // Darcy-Weisbach equation for pressure loss
    const velocityFtPerSec = velocity / 60; // Convert FPM to ft/s
    const diameterFt = hydraulicDiameter / 12; // Convert inches to feet
    const airDensity = 0.075; // lb/ft³ for air at standard conditions
    
    // Pressure loss in inches of water per 100 feet
    const pressureLossLbPerSqFt = frictionFactor * (100 / diameterFt) * (airDensity * velocityFtPerSec * velocityFtPerSec) / (2 * 32.174);
    const pressureLossInchesWater = pressureLossLbPerSqFt / 5.2; // Convert to inches of water
    
    return pressureLossInchesWater;
  }

  private checkCompliance(
    velocity: number,
    dimensions: { width?: number; height?: number; diameter?: number },
    application: string,
    subType: string,
    pressureLoss: number,
    targetFrictionRate: number
  ) {
    const warnings: string[] = [];
    const recommendations: string[] = [];
    
    // Check velocity compliance
    const velocityLimits = SMACNA_VELOCITY_LIMITS[application as keyof typeof SMACNA_VELOCITY_LIMITS][subType as keyof typeof SMACNA_VELOCITY_LIMITS.supply];
    const velocityCompliant = velocity >= velocityLimits.min && velocity <= velocityLimits.max;
    
    if (!velocityCompliant) {
      if (velocity < velocityLimits.min) {
        warnings.push(`Velocity ${velocity.toFixed(0)} FPM is below SMACNA minimum of ${velocityLimits.min} FPM`);
        recommendations.push(`Consider reducing duct size to increase velocity`);
      } else {
        warnings.push(`Velocity ${velocity.toFixed(0)} FPM exceeds SMACNA maximum of ${velocityLimits.max} FPM`);
        recommendations.push(`Consider increasing duct size to reduce velocity`);
      }
    }
    
    // Check size compliance (all standard sizes are compliant by definition)
    const sizeCompliant = true;
    
    // Check pressure loss
    const pressureLossCompliant = pressureLoss <= targetFrictionRate * 1.1; // Allow 10% tolerance
    
    if (!pressureLossCompliant) {
      warnings.push(`Pressure loss ${pressureLoss.toFixed(3)} exceeds target friction rate of ${targetFrictionRate.toFixed(3)}`);
      recommendations.push(`Consider increasing duct size to reduce pressure loss`);
    }
    
    const isCompliant = velocityCompliant && sizeCompliant && pressureLossCompliant;
    
    return {
      isCompliant,
      velocityCompliant,
      sizeCompliant,
      warnings,
      recommendations,
    };
  }

  /**
   * Get SMACNA standard sizes for UI display
   */
  public getStandardSizes(ductType: 'rectangular' | 'round') {
    if (ductType === 'rectangular') {
      return SMACNA_RECTANGULAR_SIZES;
    } else {
      return SMACNA_ROUND_SIZES.map(diameter => ({ diameter }));
    }
  }

  /**
   * Get velocity limits for specific application
   */
  public getVelocityLimits(application: string, subType: string) {
    return SMACNA_VELOCITY_LIMITS[application as keyof typeof SMACNA_VELOCITY_LIMITS]?.[subType as keyof typeof SMACNA_VELOCITY_LIMITS.supply];
  }

  /**
   * Validate duct sizing input
   */
  public validateInput(input: unknown): DuctSizingInput {
    return DuctSizingInputSchema.parse(input);
  }
}
