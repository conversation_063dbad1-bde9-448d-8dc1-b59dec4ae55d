/**
 * SizeWise Suite - Warning Panel
 * 
 * Right-edge retractable panel for displaying warnings and alerts
 */

'use client';

import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface WarningPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

export const WarningPanel: React.FC<WarningPanelProps> = ({
  isOpen,
  onToggle,
  className = '',
}) => {
  const mockWarnings = [
    { id: '1', type: 'warning', message: 'Velocity exceeds SMACNA recommendations', time: '2 min ago' },
    { id: '2', type: 'info', message: 'Standards compliance check complete', time: '5 min ago' },
  ];

  return (
    <div className={`flex ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="mr-2 w-8 h-12 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-l-lg flex items-center justify-center hover:bg-white/90 dark:hover:bg-gray-800/90 transition-colors"
      >
        {isOpen ? (
          <ChevronRightIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <ChevronLeftIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        )}
      </button>

      {/* Panel Content */}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isOpen ? 'w-80 opacity-100' : 'w-0 opacity-0'
        } overflow-hidden`}
      >
        <div className="h-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-lg shadow-lg border border-gray-200/50 dark:border-gray-700/50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 mr-2 text-yellow-500" />
                Warnings & Alerts
              </h3>
              <span className="text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                {mockWarnings.length}
              </span>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {mockWarnings.map((warning) => (
                <div key={warning.id} className="p-3 bg-white/30 dark:bg-gray-800/30 rounded-lg border-l-4 border-l-yellow-400">
                  <div className="flex items-start space-x-3">
                    <span className="text-lg flex-shrink-0 mt-0.5">⚠️</span>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm text-yellow-600 dark:text-yellow-400">
                        {warning.message}
                      </div>
                      <div className="text-xs text-gray-500 mt-2">{warning.time}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WarningPanel;
