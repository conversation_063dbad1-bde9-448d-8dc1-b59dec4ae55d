/**
 * SizeWise Suite - Duct Renderer
 * 
 * Renders 3D ducts converted from stick lines
 * Part of the 3D-first implementation
 */

'use client';

import React from 'react';
import * as THREE from 'three';

export type ViewMode = 'wireframe' | 'solid' | 'transparent' | 'xray';

export interface Duct3D {
  id: string;
  geometry: {
    type: 'rectangular' | 'round';
    width?: number;
    height?: number;
    diameter?: number;
    length: number;
  };
  position: THREE.Vector3;
  rotation: THREE.Euler;
  material: {
    type: string;
    color: string;
    roughness: number;
  };
  isSelected: boolean;
  properties: {
    airflow: number;
    velocity: number;
    pressureLoss: number;
    ductType: 'supply' | 'return' | 'exhaust';
    insulation?: {
      type: string;
      thickness: number;
    };
  };
  metadata: {
    sourceStickLineId?: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

interface DuctRendererProps {
  ducts: Duct3D[];
  selectedObjects: string[];
  viewMode: ViewMode;
}

export const DuctRenderer: React.FC<DuctRendererProps> = ({
  ducts,
  selectedObjects,
  viewMode,
}) => {
  const getMaterial = (duct: Duct3D, isSelected: boolean) => {
    const baseColor = isSelected ? '#ff6b6b' : duct.material.color;
    
    switch (viewMode) {
      case 'wireframe':
        return <meshBasicMaterial color={baseColor} wireframe />;
      case 'transparent':
        return <meshStandardMaterial color={baseColor} transparent opacity={0.7} />;
      case 'xray':
        return <meshBasicMaterial color={baseColor} transparent opacity={0.3} />;
      default:
        return <meshStandardMaterial color={baseColor} roughness={duct.material.roughness} />;
    }
  };

  const renderRectangularDuct = (duct: Duct3D) => {
    const isSelected = selectedObjects.includes(duct.id);
    const { width = 12, height = 8, length } = duct.geometry;
    
    // Convert inches to feet for 3D display
    const widthFt = width / 12;
    const heightFt = height / 12;
    const lengthFt = length / 12;
    
    return (
      <mesh
        key={duct.id}
        position={[duct.position.x, duct.position.y, duct.position.z]}
        rotation={[duct.rotation.x, duct.rotation.y, duct.rotation.z]}
        castShadow
        receiveShadow
      >
        <boxGeometry args={[widthFt, heightFt, lengthFt]} />
        {getMaterial(duct, isSelected)}
        
        {/* Wireframe overlay for selected ducts */}
        {isSelected && (
          <mesh>
            <boxGeometry args={[widthFt, heightFt, lengthFt]} />
            <meshBasicMaterial color="#ff6b6b" wireframe />
          </mesh>
        )}
      </mesh>
    );
  };

  const renderRoundDuct = (duct: Duct3D) => {
    const isSelected = selectedObjects.includes(duct.id);
    const { diameter = 10, length } = duct.geometry;
    
    // Convert inches to feet for 3D display
    const radiusFt = diameter / 24; // diameter to radius, inches to feet
    const lengthFt = length / 12;
    
    return (
      <mesh
        key={duct.id}
        position={[duct.position.x, duct.position.y, duct.position.z]}
        rotation={[duct.rotation.x, duct.rotation.y, duct.rotation.z]}
        castShadow
        receiveShadow
      >
        <cylinderGeometry args={[radiusFt, radiusFt, lengthFt, 16]} />
        {getMaterial(duct, isSelected)}
        
        {/* Wireframe overlay for selected ducts */}
        {isSelected && (
          <mesh>
            <cylinderGeometry args={[radiusFt, radiusFt, lengthFt, 16]} />
            <meshBasicMaterial color="#ff6b6b" wireframe />
          </mesh>
        )}
      </mesh>
    );
  };

  return (
    <group name="ducts">
      {ducts.map((duct) => {
        if (duct.geometry.type === 'rectangular') {
          return renderRectangularDuct(duct);
        } else {
          return renderRoundDuct(duct);
        }
      })}
      
      {/* Demo ducts for testing */}
      {ducts.length === 0 && (
        <>
          {/* Demo rectangular duct */}
          <mesh position={[2, 1, 0]} castShadow receiveShadow>
            <boxGeometry args={[1, 0.67, 3]} />
            <meshStandardMaterial color="#c0c0c0" roughness={0.3} />
          </mesh>
          
          {/* Demo round duct */}
          <mesh position={[-2, 1, 0]} rotation={[0, 0, Math.PI / 2]} castShadow receiveShadow>
            <cylinderGeometry args={[0.42, 0.42, 3, 16]} />
            <meshStandardMaterial color="#c0c0c0" roughness={0.3} />
          </mesh>
        </>
      )}
    </group>
  );
};

export default DuctRenderer;
