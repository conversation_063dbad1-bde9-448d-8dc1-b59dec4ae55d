/**
 * SizeWise Suite Feature Flags
 * 
 * Controls progressive rollout of new features during migration
 * All major changes must be behind feature flags per Protocol
 */

export interface FeatureFlags {
  // Migration flags
  enableCenteredNavigation: boolean;
  enable3DCanvas: boolean;
  enableNewAirDuctSizer: boolean;
  
  // Feature flags
  enableProjectProperties: boolean;
  enableStandardsCompliance: boolean;
  enableAdvancedCalculations: boolean;
  
  // UI flags
  enableGlassmorphismIntegration: boolean;
  enableNewThemeSystem: boolean;
}

export const defaultFeatureFlags: FeatureFlags = {
  // Migration flags - enabled for demonstration
  enableCenteredNavigation: true,
  enable3DCanvas: true,
  enableNewAirDuctSizer: true,

  // Feature flags - progressive enablement
  enableProjectProperties: true,
  enableStandardsCompliance: true, // Core requirement
  enableAdvancedCalculations: true,

  // UI flags
  enableGlassmorphismIntegration: true,
  enableNewThemeSystem: true,
};

// Environment-based overrides
export const getFeatureFlags = (): FeatureFlags => {
  const env = process.env.NODE_ENV;
  
  if (env === 'development') {
    return {
      ...defaultFeatureFlags,
      // Enable all features in development
      enableCenteredNavigation: true,
      enable3DCanvas: true,
      enableNewAirDuctSizer: true,
      enableProjectProperties: true,
      enableAdvancedCalculations: true,
      enableGlassmorphismIntegration: true,
      enableNewThemeSystem: true,
    };
  }
  
  return defaultFeatureFlags;
};
