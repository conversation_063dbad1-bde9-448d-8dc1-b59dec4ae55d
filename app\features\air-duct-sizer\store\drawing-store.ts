/**
 * SizeWise Suite - Drawing Tools Store
 * 
 * State management for drawing tools and interactions
 * Supports stick line drawing and 3D object placement
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import * as THREE from 'three';

export type DrawingTool = 
  | 'select' 
  | 'stick-line' 
  | 'rectangle-duct' 
  | 'round-duct' 
  | 'equipment' 
  | 'measure' 
  | 'pan' 
  | 'zoom';

export interface DrawingState {
  // Current tool
  drawingTool: DrawingTool;
  
  // Drawing state
  isDrawing: boolean;
  currentStickLine: THREE.Vector3[] | null;
  
  // Tool settings
  stickLineSettings: {
    color: string;
    thickness: number;
    snapToGrid: boolean;
    ductType: 'supply' | 'return' | 'exhaust';
    airflow: number;
  };
  
  ductSettings: {
    type: 'rectangular' | 'round';
    width: number;
    height: number;
    diameter: number;
    material: string;
    insulation: boolean;
    insulationThickness: number;
  };
  
  equipmentSettings: {
    type: 'fan' | 'damper' | 'diffuser' | 'grille' | 'coil' | 'filter';
    size: 'small' | 'medium' | 'large';
    model: string;
  };
  
  // Interaction state
  mousePosition: THREE.Vector2;
  worldPosition: THREE.Vector3;
  isShiftPressed: boolean;
  isCtrlPressed: boolean;
  isAltPressed: boolean;
  
  // Measurement
  measurementPoints: THREE.Vector3[];
  showMeasurements: boolean;
}

export interface DrawingActions {
  // Tool selection
  setDrawingTool: (tool: DrawingTool) => void;
  
  // Stick line drawing
  startStickLine: (point: THREE.Vector3) => void;
  updateStickLine: (point: THREE.Vector3) => void;
  finishStickLine: () => void;
  cancelStickLine: () => void;
  
  // Tool settings
  updateStickLineSettings: (settings: Partial<DrawingState['stickLineSettings']>) => void;
  updateDuctSettings: (settings: Partial<DrawingState['ductSettings']>) => void;
  updateEquipmentSettings: (settings: Partial<DrawingState['equipmentSettings']>) => void;
  
  // Interaction
  setMousePosition: (position: THREE.Vector2) => void;
  setWorldPosition: (position: THREE.Vector3) => void;
  setKeyPressed: (key: 'shift' | 'ctrl' | 'alt', pressed: boolean) => void;
  
  // Measurement
  addMeasurementPoint: (point: THREE.Vector3) => void;
  clearMeasurements: () => void;
  toggleMeasurements: () => void;
  
  // Utility
  reset: () => void;
}

type DrawingStore = DrawingState & DrawingActions;

const initialState: DrawingState = {
  drawingTool: 'select',
  isDrawing: false,
  currentStickLine: null,
  
  stickLineSettings: {
    color: '#3b82f6',
    thickness: 2,
    snapToGrid: true,
    ductType: 'supply',
    airflow: 1000,
  },
  
  ductSettings: {
    type: 'rectangular',
    width: 12,
    height: 8,
    diameter: 10,
    material: 'galvanized-steel',
    insulation: false,
    insulationThickness: 1,
  },
  
  equipmentSettings: {
    type: 'fan',
    size: 'medium',
    model: '',
  },
  
  mousePosition: new THREE.Vector2(0, 0),
  worldPosition: new THREE.Vector3(0, 0, 0),
  isShiftPressed: false,
  isCtrlPressed: false,
  isAltPressed: false,
  
  measurementPoints: [],
  showMeasurements: true,
};

export const useDrawingStore = create<DrawingStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Tool selection
      setDrawingTool: (tool) => {
        // Cancel any current drawing when switching tools
        if (get().isDrawing) {
          get().cancelStickLine();
        }
        set({ drawingTool: tool });
      },

      // Stick line drawing
      startStickLine: (point) => {
        const { snapToGrid, gridSize } = { snapToGrid: true, gridSize: 1 }; // Would come from canvas store
        
        const snappedPoint = snapToGrid 
          ? new THREE.Vector3(
              Math.round(point.x / gridSize) * gridSize,
              Math.round(point.y / gridSize) * gridSize,
              Math.round(point.z / gridSize) * gridSize
            )
          : point;

        set({
          isDrawing: true,
          currentStickLine: [snappedPoint],
        });
      },

      updateStickLine: (point) => {
        const state = get();
        if (!state.isDrawing || !state.currentStickLine) return;

        const { snapToGrid, gridSize } = { snapToGrid: true, gridSize: 1 }; // Would come from canvas store
        
        const snappedPoint = snapToGrid 
          ? new THREE.Vector3(
              Math.round(point.x / gridSize) * gridSize,
              Math.round(point.y / gridSize) * gridSize,
              Math.round(point.z / gridSize) * gridSize
            )
          : point;

        // Update the last point or add a new one
        const updatedLine = [...state.currentStickLine];
        if (updatedLine.length === 1) {
          updatedLine.push(snappedPoint);
        } else {
          updatedLine[updatedLine.length - 1] = snappedPoint;
        }

        set({ currentStickLine: updatedLine });
      },

      finishStickLine: () => {
        const state = get();
        if (!state.currentStickLine || state.currentStickLine.length < 2) {
          get().cancelStickLine();
          return;
        }

        // Add the stick line to the canvas store
        // This would typically be done through a callback or by importing the canvas store
        console.log('Stick line completed:', state.currentStickLine);

        set({
          isDrawing: false,
          currentStickLine: null,
        });
      },

      cancelStickLine: () => {
        set({
          isDrawing: false,
          currentStickLine: null,
        });
      },

      // Tool settings
      updateStickLineSettings: (settings) => {
        set((state) => ({
          stickLineSettings: { ...state.stickLineSettings, ...settings },
        }));
      },

      updateDuctSettings: (settings) => {
        set((state) => ({
          ductSettings: { ...state.ductSettings, ...settings },
        }));
      },

      updateEquipmentSettings: (settings) => {
        set((state) => ({
          equipmentSettings: { ...state.equipmentSettings, ...settings },
        }));
      },

      // Interaction
      setMousePosition: (position) => {
        set({ mousePosition: position });
      },

      setWorldPosition: (position) => {
        set({ worldPosition: position });
      },

      setKeyPressed: (key, pressed) => {
        switch (key) {
          case 'shift':
            set({ isShiftPressed: pressed });
            break;
          case 'ctrl':
            set({ isCtrlPressed: pressed });
            break;
          case 'alt':
            set({ isAltPressed: pressed });
            break;
        }
      },

      // Measurement
      addMeasurementPoint: (point) => {
        set((state) => ({
          measurementPoints: [...state.measurementPoints, point],
        }));
      },

      clearMeasurements: () => {
        set({ measurementPoints: [] });
      },

      toggleMeasurements: () => {
        set((state) => ({ showMeasurements: !state.showMeasurements }));
      },

      // Utility
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'drawing-store',
    }
  )
);
