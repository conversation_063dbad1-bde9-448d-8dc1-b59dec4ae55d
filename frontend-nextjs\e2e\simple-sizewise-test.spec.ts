/**
 * SizeWise Suite - Simple Playwright Test
 * 
 * Basic test to verify the application is working
 */

import { test, expect } from '@playwright/test';

test.describe('SizeWise Suite Basic Tests', () => {
  
  test('should load main application', async ({ page }) => {
    await page.goto('http://localhost:3000');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Check title
    await expect(page).toHaveTitle(/SizeWise/);
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/main-page.png', fullPage: true });
    
    console.log('✅ Main application loaded successfully');
  });

  test('should have centered navigation', async ({ page }) => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Check for navigation
    const nav = page.locator('nav');
    await expect(nav).toBeVisible();
    
    // Check for SizeWise Suite text
    const logo = page.locator('text=SizeWise Suite');
    await expect(logo).toBeVisible();
    
    await page.screenshot({ path: 'test-results/navigation.png' });
    
    console.log('✅ Centered navigation is present');
  });

  test('should load Air Duct Sizer page', async ({ page }) => {
    await page.goto('http://localhost:3000/air-duct-sizer-new');
    await page.waitForLoadState('networkidle');
    
    // Should not have critical errors
    await expect(page).toHaveURL(/air-duct-sizer-new/);
    
    await page.screenshot({ path: 'test-results/air-duct-sizer.png', fullPage: true });
    
    console.log('✅ Air Duct Sizer page loaded');
  });

  test('should not have module resolution errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Filter critical errors
    const criticalErrors = errors.filter(error => 
      error.includes('Module not found') ||
      error.includes('Cannot resolve') ||
      error.includes('@heroicons') ||
      error.includes('AppShell')
    );
    
    console.log('Console errors:', errors);
    console.log('Critical errors:', criticalErrors);
    
    expect(criticalErrors.length).toBe(0);
    
    console.log('✅ No critical module resolution errors');
  });
});
