/**
 * SizeWise Suite - View Cube
 * 
 * Top-right 3D navigation aid for camera control
 * Part of the 9 required UI elements for Air Duct Sizer
 */

'use client';

import React from 'react';

interface ViewCubeProps {
  className?: string;
}

export const ViewCube: React.FC<ViewCubeProps> = ({ className = '' }) => {
  const handleViewChange = (view: string) => {
    console.log(`Changing view to: ${view}`);
    // This would integrate with the 3D canvas camera controls
  };

  return (
    <div className={`${className}`}>
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
        <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
          View Cube
        </div>
        
        {/* 3D Cube Representation */}
        <div className="relative w-16 h-16 mx-auto">
          {/* Top face */}
          <button
            onClick={() => handleViewChange('top')}
            className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-200 hover:bg-blue-300 border border-blue-400 text-xs font-medium text-blue-800 flex items-center justify-center transition-colors"
            style={{ clipPath: 'polygon(25% 0%, 75% 0%, 100% 100%, 0% 100%)' }}
            title="Top View"
          >
            T
          </button>
          
          {/* Left face */}
          <button
            onClick={() => handleViewChange('left')}
            className="absolute top-4 left-0 w-4 h-8 bg-green-200 hover:bg-green-300 border border-green-400 text-xs font-medium text-green-800 flex items-center justify-center transition-colors"
            style={{ clipPath: 'polygon(0% 0%, 100% 25%, 100% 75%, 0% 100%)' }}
            title="Left View"
          >
            L
          </button>
          
          {/* Front face */}
          <button
            onClick={() => handleViewChange('front')}
            className="absolute top-4 left-4 w-8 h-8 bg-gray-200 hover:bg-gray-300 border border-gray-400 text-xs font-medium text-gray-800 flex items-center justify-center transition-colors"
            title="Front View"
          >
            F
          </button>
          
          {/* Right face */}
          <button
            onClick={() => handleViewChange('right')}
            className="absolute top-4 right-0 w-4 h-8 bg-red-200 hover:bg-red-300 border border-red-400 text-xs font-medium text-red-800 flex items-center justify-center transition-colors"
            style={{ clipPath: 'polygon(0% 25%, 100% 0%, 100% 100%, 0% 75%)' }}
            title="Right View"
          >
            R
          </button>
          
          {/* Bottom face */}
          <button
            onClick={() => handleViewChange('bottom')}
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-yellow-200 hover:bg-yellow-300 border border-yellow-400 text-xs font-medium text-yellow-800 flex items-center justify-center transition-colors"
            style={{ clipPath: 'polygon(0% 0%, 100% 0%, 75% 100%, 25% 100%)' }}
            title="Bottom View"
          >
            B
          </button>
        </div>
        
        {/* Quick View Buttons */}
        <div className="mt-3 space-y-1">
          <button
            onClick={() => handleViewChange('isometric')}
            className="w-full px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Isometric
          </button>
          <button
            onClick={() => handleViewChange('home')}
            className="w-full px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Home View
          </button>
        </div>
      </div>
    </div>
  );
};
