/**
 * SizeWise Suite - Manual E2E Verification Script
 * 
 * Verifies all implemented features against requirements
 */

const fs = require('fs');
const path = require('path');

class E2EVerifier {
  constructor() {
    this.projectRoot = path.join(__dirname, '../..');
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  test(description, testFn) {
    try {
      testFn();
      this.results.passed++;
      this.results.tests.push({ description, status: '✅ PASS' });
      console.log(`✅ PASS: ${description}`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ description, status: '❌ FAIL', error: error.message });
      console.log(`❌ FAIL: ${description} - ${error.message}`);
    }
  }

  fileExists(filePath) {
    const fullPath = path.join(this.projectRoot, filePath);
    if (!fs.existsSync(fullPath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    return fs.readFileSync(fullPath, 'utf8');
  }

  fileContains(filePath, content) {
    const fileContent = this.fileExists(filePath);
    if (!fileContent.includes(content)) {
      throw new Error(`File ${filePath} does not contain: ${content}`);
    }
    return fileContent;
  }

  runAllTests() {
    console.log('🧪 Starting SizeWise Suite E2E Verification...\n');

    // 1. Navigation System Tests
    console.log('📍 1. Navigation System Testing');
    this.test('Centered Navigation Component exists', () => {
      this.fileExists('app/shared/components/navigation/CenteredNavigation.tsx');
    });

    this.test('Navigation has required menu structure', () => {
      this.fileContains('app/shared/components/navigation/CenteredNavigation.tsx', 'Home');
      this.fileContains('app/shared/components/navigation/CenteredNavigation.tsx', 'File');
      this.fileContains('app/shared/components/navigation/CenteredNavigation.tsx', 'Projects');
      this.fileContains('app/shared/components/navigation/CenteredNavigation.tsx', 'Tools');
      this.fileContains('app/shared/components/navigation/CenteredNavigation.tsx', 'Profile');
    });

    this.test('New AppShell uses centered navigation', () => {
      this.fileContains('app/core/layout/AppShell.tsx', 'CenteredNavigation');
      this.fileContains('app/core/layout/AppShell.tsx', 'enableCenteredNavigation');
    });

    // 2. Air Duct Sizer - 9 UI Elements
    console.log('\n🏗️ 2. Air Duct Sizer - 9 Required UI Elements');
    
    this.test('1. Project Properties Panel implemented', () => {
      const content = this.fileExists('app/features/air-duct-sizer/components/panels/ProjectPropertiesPanel.tsx');
      if (!content.includes('info') || !content.includes('codes') || !content.includes('defaults') || 
          !content.includes('team') || !content.includes('admin')) {
        throw new Error('Missing required 5 sections');
      }
    });

    this.test('2. 3D Canvas Workspace with Three.js', () => {
      this.fileContains('app/features/air-duct-sizer/components/workspace/Canvas3D.tsx', '@react-three/fiber');
      this.fileContains('app/features/air-duct-sizer/components/workspace/Canvas3D.tsx', 'Canvas');
      this.fileContains('app/features/air-duct-sizer/components/workspace/Canvas3D.tsx', 'OrbitControls');
    });

    this.test('3. Drawing Tool FAB bottom-right', () => {
      this.fileContains('app/features/air-duct-sizer/components/ui/DrawingFAB.tsx', 'bottom-right');
      this.fileContains('app/features/air-duct-sizer/components/ui/DrawingFAB.tsx', 'stick-line');
    });

    this.test('4. View Cube top-right 3D navigation', () => {
      this.fileContains('app/features/air-duct-sizer/components/ui/ViewCube.tsx', 'top-right');
      this.fileContains('app/features/air-duct-sizer/components/ui/ViewCube.tsx', '3D navigation');
    });

    this.test('5. Calculation Bar bottom full-width', () => {
      this.fileContains('app/features/air-duct-sizer/components/panels/CalculationBar.tsx', 'bottom full-width');
      this.fileContains('app/features/air-duct-sizer/components/panels/CalculationBar.tsx', 'calculation results');
    });

    this.test('6. Import/Export Panel collapsible', () => {
      this.fileContains('app/features/air-duct-sizer/components/panels/ImportExportPanel.tsx', 'collapsible');
      this.fileContains('app/features/air-duct-sizer/components/panels/ImportExportPanel.tsx', 'Import');
      this.fileContains('app/features/air-duct-sizer/components/panels/ImportExportPanel.tsx', 'Export');
    });

    this.test('7. Warning Panel right-edge retractable', () => {
      this.fileContains('app/features/air-duct-sizer/components/panels/WarningPanel.tsx', 'right-edge');
      this.fileContains('app/features/air-duct-sizer/components/panels/WarningPanel.tsx', 'retractable');
    });

    this.test('8. Selection Pop-Up contextual', () => {
      this.fileContains('app/features/air-duct-sizer/components/ui/SelectionPopUp.tsx', 'contextual');
      this.fileContains('app/features/air-duct-sizer/components/ui/SelectionPopUp.tsx', 'selectedObjects');
    });

    this.test('9. Drawing Toolbar left-side', () => {
      this.fileContains('app/features/air-duct-sizer/components/ui/DrawingToolbar.tsx', 'left-side');
      this.fileContains('app/features/air-duct-sizer/components/ui/DrawingToolbar.tsx', 'drawing tools');
    });

    // 3. 3D System Tests
    console.log('\n🎮 3. 3D Canvas and Rendering System');
    
    this.test('Stick Line Renderer implemented', () => {
      this.fileContains('app/features/air-duct-sizer/components/workspace/StickLineRenderer.tsx', 'StickLineRenderer');
      this.fileContains('app/features/air-duct-sizer/components/workspace/StickLineRenderer.tsx', 'Line');
    });

    this.test('3D Duct Renderer with geometry types', () => {
      const content = this.fileContains('app/features/air-duct-sizer/components/workspace/DuctRenderer.tsx', 'DuctRenderer');
      if (!content.includes('rectangular') || !content.includes('round') || 
          !content.includes('boxGeometry') || !content.includes('cylinderGeometry')) {
        throw new Error('Missing duct geometry types');
      }
    });

    this.test('Equipment Renderer with HVAC equipment', () => {
      const content = this.fileContains('app/features/air-duct-sizer/components/workspace/EquipmentRenderer.tsx', 'EquipmentRenderer');
      if (!content.includes('fan') || !content.includes('damper') || !content.includes('diffuser')) {
        throw new Error('Missing HVAC equipment types');
      }
    });

    // 4. State Management
    console.log('\n🗃️ 4. State Management System');
    
    this.test('3D Canvas Store with conversion capability', () => {
      this.fileContains('app/features/air-duct-sizer/store/canvas-store.ts', 'useCanvas3DStore');
      this.fileContains('app/features/air-duct-sizer/store/canvas-store.ts', 'convertStickLineToDuct');
      this.fileContains('app/features/air-duct-sizer/store/canvas-store.ts', 'StickLine');
      this.fileContains('app/features/air-duct-sizer/store/canvas-store.ts', 'Duct3D');
    });

    this.test('Drawing Store with tool management', () => {
      this.fileContains('app/features/air-duct-sizer/store/drawing-store.ts', 'useDrawingStore');
      this.fileContains('app/features/air-duct-sizer/store/drawing-store.ts', 'startStickLine');
      this.fileContains('app/features/air-duct-sizer/store/drawing-store.ts', 'finishStickLine');
    });

    // 5. Standards Compliance
    console.log('\n📏 5. Standards Compliance System');
    
    this.test('SMACNA Service with versioning', () => {
      const content = this.fileContains('app/features/standards-compliance/services/smacna-service.ts', 'SMACNAService');
      if (!content.includes('SMACNA_VERSION') || !content.includes('calculateDuctSizing') || 
          !content.includes('velocity limits')) {
        throw new Error('Missing SMACNA service features');
      }
    });

    this.test('Standards compliance calculations', () => {
      this.fileContains('app/features/standards-compliance/services/smacna-service.ts', 'pressure loss');
      this.fileContains('app/features/standards-compliance/services/smacna-service.ts', 'friction factor');
      this.fileContains('app/features/standards-compliance/services/smacna-service.ts', 'compliance');
    });

    // 6. Feature Flags
    console.log('\n🚩 6. Feature Flag System');
    
    this.test('Comprehensive feature flags configuration', () => {
      const content = this.fileContains('app/config/feature-flags.ts', 'FeatureFlags');
      if (!content.includes('enableCenteredNavigation') || !content.includes('enable3DCanvas') || 
          !content.includes('enableNewAirDuctSizer')) {
        throw new Error('Missing required feature flags');
      }
    });

    this.test('Feature flag hooks implementation', () => {
      this.fileContains('app/core/hooks/useFeatureFlags.ts', 'useFeatureFlags');
      this.fileContains('app/core/hooks/useFeatureFlags.ts', 'setFeatureFlagOverride');
    });

    // 7. Migration System
    console.log('\n🔄 7. Migration and Backup System');
    
    this.test('Migration scripts exist', () => {
      this.fileExists('scripts/migration/01-create-directory-structure.js');
      this.fileExists('scripts/migration/backup-and-rollback.js');
    });

    this.test('ADR documentation approved', () => {
      this.fileContains('docs/architecture/decisions/ADR-001-architecture-migration-to-feature-based-3d-first.md', 'APPROVED');
      this.fileContains('docs/architecture/decisions/ADR-001-architecture-migration-to-feature-based-3d-first.md', 'feature-based architecture');
    });

    // 8. Dependencies
    console.log('\n📦 8. Package Dependencies');
    
    this.test('Three.js dependencies installed', () => {
      const packageContent = this.fileExists('frontend-nextjs/package.json');
      const packageJson = JSON.parse(packageContent);
      
      if (!packageJson.dependencies.three || !packageJson.dependencies['@react-three/fiber'] || 
          !packageJson.dependencies['@react-three/drei']) {
        throw new Error('Missing Three.js dependencies');
      }
    });

    // 9. Routes
    console.log('\n🛣️ 9. Route Configuration');
    
    this.test('New Air Duct Sizer route exists', () => {
      this.fileContains('frontend-nextjs/app/air-duct-sizer-new/page.tsx', 'AirDuctSizerPage');
      this.fileContains('frontend-nextjs/app/air-duct-sizer-new/page.tsx', '@/features/air-duct-sizer/page');
    });

    this.test('Main layout updated', () => {
      this.fileContains('frontend-nextjs/app/layout.tsx', '@/core/layout/AppShell');
    });

    // Performance Tests
    console.log('\n⚡ 10. Performance and Integration');
    
    this.test('Performance monitoring in 3D canvas', () => {
      this.fileContains('app/features/air-duct-sizer/components/workspace/Canvas3D.tsx', 'PerformanceMonitor');
      this.fileContains('app/features/air-duct-sizer/components/workspace/Canvas3D.tsx', 'fps');
    });

    this.test('Fallback mechanisms for feature flags', () => {
      this.fileContains('app/core/layout/AppShell.tsx', 'LegacyAppShell');
      this.fileContains('app/features/air-duct-sizer/components/layout/AirDuctSizerLayout.tsx', 'enableNewAirDuctSizer');
    });

    // Final Results
    console.log('\n📊 Test Results Summary');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
    
    if (this.results.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Implementation is compliant with requirements.');
    } else {
      console.log('\n⚠️  Some tests failed. Review the issues above.');
    }

    return this.results;
  }
}

// Run the verification
const verifier = new E2EVerifier();
verifier.runAllTests();
