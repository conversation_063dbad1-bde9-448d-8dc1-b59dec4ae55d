/**
 * SizeWise Suite - Manual Browser Verification
 * 
 * Uses browser automation to verify the application functionality
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class BrowserVerifier {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async init() {
    console.log('🚀 Starting Browser Verification...\n');
    this.browser = await chromium.launch({ headless: false });
    this.page = await this.browser.newPage();
    
    // Set up console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Console Error: ${msg.text()}`);
      }
    });
  }

  async test(description, testFn) {
    try {
      console.log(`🧪 Testing: ${description}`);
      await testFn();
      this.results.passed++;
      this.results.tests.push({ description, status: '✅ PASS' });
      console.log(`✅ PASS: ${description}\n`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ description, status: '❌ FAIL', error: error.message });
      console.log(`❌ FAIL: ${description} - ${error.message}\n`);
    }
  }

  async runAllTests() {
    await this.init();

    // Test 1: Main Application Load
    await this.test('Main application loads correctly', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Check title
      const title = await this.page.title();
      if (!title.includes('SizeWise')) {
        throw new Error(`Expected title to contain 'SizeWise', got: ${title}`);
      }
      
      // Take screenshot
      await this.page.screenshot({ path: 'test-results/main-application.png', fullPage: true });
    });

    // Test 2: Centered Navigation
    await this.test('Centered navigation is present and functional', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Check for navigation
      const nav = this.page.locator('nav');
      await nav.waitFor({ state: 'visible' });
      
      // Check for SizeWise Suite logo/text
      const logo = this.page.locator('text=SizeWise Suite');
      await logo.waitFor({ state: 'visible' });
      
      await this.page.screenshot({ path: 'test-results/centered-navigation.png' });
    });

    // Test 3: Navigation Items
    await this.test('All 5 navigation items are present', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Check for navigation items
      const navItems = ['Home', 'File', 'Projects', 'Tools', 'Profile'];
      for (const item of navItems) {
        const navItem = this.page.locator(`text=${item}`).first();
        await navItem.waitFor({ state: 'visible', timeout: 5000 });
      }
    });

    // Test 4: Air Duct Sizer Direct Access
    await this.test('Air Duct Sizer page loads directly', async () => {
      await this.page.goto('http://localhost:3000/air-duct-sizer-new');
      await this.page.waitForLoadState('networkidle');
      
      // Wait for any dynamic content
      await this.page.waitForTimeout(2000);
      
      // Check URL
      const url = this.page.url();
      if (!url.includes('air-duct-sizer-new')) {
        throw new Error(`Expected URL to contain 'air-duct-sizer-new', got: ${url}`);
      }
      
      await this.page.screenshot({ path: 'test-results/air-duct-sizer-page.png', fullPage: true });
    });

    // Test 5: No Critical Console Errors
    await this.test('No critical module resolution errors', async () => {
      const errors = [];
      
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(3000);
      
      // Filter critical errors
      const criticalErrors = errors.filter(error => 
        error.includes('Module not found') ||
        error.includes('Cannot resolve') ||
        error.includes('@heroicons') ||
        error.includes('AppShell')
      );
      
      console.log(`   Found ${errors.length} console errors, ${criticalErrors.length} critical`);
      
      if (criticalErrors.length > 0) {
        throw new Error(`Critical errors found: ${criticalErrors.join(', ')}`);
      }
    });

    // Test 6: Performance Check
    await this.test('Application loads within reasonable time', async () => {
      const startTime = Date.now();
      
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      console.log(`   Load time: ${loadTime}ms`);
      
      if (loadTime > 10000) {
        throw new Error(`Load time too slow: ${loadTime}ms`);
      }
    });

    await this.cleanup();
    return this.generateReport();
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  generateReport() {
    console.log('\n📊 Browser Verification Results');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        passed: this.results.passed,
        failed: this.results.failed,
        successRate: ((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)
      },
      tests: this.results.tests,
      screenshots: [
        'test-results/main-application.png',
        'test-results/centered-navigation.png',
        'test-results/air-duct-sizer-page.png'
      ]
    };
    
    // Ensure test-results directory exists
    const testResultsDir = path.join(__dirname, 'test-results');
    if (!fs.existsSync(testResultsDir)) {
      fs.mkdirSync(testResultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(testResultsDir, 'browser-verification-report.json'),
      JSON.stringify(report, null, 2)
    );
    
    if (this.results.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! SizeWise Suite architecture migration is working correctly!');
    } else {
      console.log('\n⚠️  Some tests failed. Check the details above.');
    }
    
    console.log('\n📄 Detailed report saved to: test-results/browser-verification-report.json');
    console.log('📸 Screenshots saved to: test-results/');
    
    return report;
  }
}

// Run the verification
async function runVerification() {
  const verifier = new BrowserVerifier();
  try {
    await verifier.runAllTests();
  } catch (error) {
    console.error('❌ Verification failed:', error);
    await verifier.cleanup();
  }
}

runVerification();
