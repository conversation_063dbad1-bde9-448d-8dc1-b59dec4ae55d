/**
 * SizeWise Suite - 3D Canvas Store
 * 
 * State management for 3D canvas workspace
 * Supports stick lines, 3D ducts, and equipment
 * 
 * Required by SizeWise Task V1 for 3D-first implementation
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import * as THREE from 'three';

// 3D object types
export interface StickLine {
  id: string;
  points: THREE.Vector3[];
  color: string;
  thickness: number;
  isSelected: boolean;
  metadata: {
    airflow?: number;
    ductType?: 'supply' | 'return' | 'exhaust';
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface Duct3D {
  id: string;
  geometry: {
    type: 'rectangular' | 'round';
    width?: number;
    height?: number;
    diameter?: number;
    length: number;
  };
  position: THREE.Vector3;
  rotation: THREE.Euler;
  material: {
    type: string;
    color: string;
    roughness: number;
  };
  isSelected: boolean;
  properties: {
    airflow: number;
    velocity: number;
    pressureLoss: number;
    ductType: 'supply' | 'return' | 'exhaust';
    insulation?: {
      type: string;
      thickness: number;
    };
  };
  metadata: {
    sourceStickLineId?: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface Equipment3D {
  id: string;
  type: 'fan' | 'damper' | 'diffuser' | 'grille' | 'coil' | 'filter';
  position: THREE.Vector3;
  rotation: THREE.Euler;
  scale: THREE.Vector3;
  isSelected: boolean;
  properties: {
    model?: string;
    manufacturer?: string;
    airflow?: number;
    pressureDrop?: number;
    specifications: Record<string, any>;
  };
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  };
}

export type ViewMode = 'wireframe' | 'solid' | 'transparent' | 'xray';

export interface CanvasState {
  // 3D objects
  stickLines: StickLine[];
  ducts: Duct3D[];
  equipment: Equipment3D[];
  
  // Selection
  selectedObjects: string[];
  
  // View settings
  viewMode: ViewMode;
  gridVisible: boolean;
  snapToGrid: boolean;
  gridSize: number;
  
  // Canvas settings
  canvasSize: { width: number; height: number };
  cameraPosition: THREE.Vector3;
  cameraTarget: THREE.Vector3;
  
  // Performance settings
  enableShadows: boolean;
  enableAntialiasing: boolean;
  maxObjects: number;
}

export interface CanvasActions {
  // Stick line operations
  addStickLine: (stickLine: Omit<StickLine, 'id' | 'metadata'>) => void;
  updateStickLine: (id: string, updates: Partial<StickLine>) => void;
  removeStickLine: (id: string) => void;
  
  // Duct operations
  addDuct: (duct: Omit<Duct3D, 'id' | 'metadata'>) => void;
  updateDuct: (id: string, updates: Partial<Duct3D>) => void;
  removeDuct: (id: string) => void;
  
  // Equipment operations
  addEquipment: (equipment: Omit<Equipment3D, 'id' | 'metadata'>) => void;
  updateEquipment: (id: string, updates: Partial<Equipment3D>) => void;
  removeEquipment: (id: string) => void;
  
  // Selection operations
  selectObject: (id: string) => void;
  selectMultiple: (ids: string[]) => void;
  clearSelection: () => void;
  toggleSelection: (id: string) => void;
  
  // Conversion operations
  convertStickLineToDuct: (stickLineId: string, ductProperties: Partial<Duct3D>) => void;
  
  // View operations
  setViewMode: (mode: ViewMode) => void;
  toggleGrid: () => void;
  toggleSnapToGrid: () => void;
  setGridSize: (size: number) => void;
  
  // Canvas operations
  setCanvasSize: (width: number, height: number) => void;
  setCameraPosition: (position: THREE.Vector3) => void;
  setCameraTarget: (target: THREE.Vector3) => void;
  
  // Utility operations
  clearAll: () => void;
  getObjectById: (id: string) => StickLine | Duct3D | Equipment3D | null;
  getSelectedObjects: () => (StickLine | Duct3D | Equipment3D)[];
}

type CanvasStore = CanvasState & CanvasActions;

const initialState: CanvasState = {
  stickLines: [],
  ducts: [],
  equipment: [],
  selectedObjects: [],
  viewMode: 'solid',
  gridVisible: true,
  snapToGrid: true,
  gridSize: 1,
  canvasSize: { width: 800, height: 600 },
  cameraPosition: new THREE.Vector3(10, 10, 10),
  cameraTarget: new THREE.Vector3(0, 0, 0),
  enableShadows: true,
  enableAntialiasing: true,
  maxObjects: 1000,
};

export const useCanvas3DStore = create<CanvasStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Stick line operations
      addStickLine: (stickLineData) => {
        const stickLine: StickLine = {
          ...stickLineData,
          id: `stick-line-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          metadata: {
            ...stickLineData.metadata,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        };
        
        set((state) => ({
          stickLines: [...state.stickLines, stickLine],
        }));
      },

      updateStickLine: (id, updates) => {
        set((state) => ({
          stickLines: state.stickLines.map((line) =>
            line.id === id
              ? { ...line, ...updates, metadata: { ...line.metadata, updatedAt: new Date() } }
              : line
          ),
        }));
      },

      removeStickLine: (id) => {
        set((state) => ({
          stickLines: state.stickLines.filter((line) => line.id !== id),
          selectedObjects: state.selectedObjects.filter((objId) => objId !== id),
        }));
      },

      // Duct operations
      addDuct: (ductData) => {
        const duct: Duct3D = {
          ...ductData,
          id: `duct-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          metadata: {
            ...ductData.metadata,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        };
        
        set((state) => ({
          ducts: [...state.ducts, duct],
        }));
      },

      updateDuct: (id, updates) => {
        set((state) => ({
          ducts: state.ducts.map((duct) =>
            duct.id === id
              ? { ...duct, ...updates, metadata: { ...duct.metadata, updatedAt: new Date() } }
              : duct
          ),
        }));
      },

      removeDuct: (id) => {
        set((state) => ({
          ducts: state.ducts.filter((duct) => duct.id !== id),
          selectedObjects: state.selectedObjects.filter((objId) => objId !== id),
        }));
      },

      // Equipment operations
      addEquipment: (equipmentData) => {
        const equipment: Equipment3D = {
          ...equipmentData,
          id: `equipment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          metadata: {
            ...equipmentData.metadata,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        };
        
        set((state) => ({
          equipment: [...state.equipment, equipment],
        }));
      },

      updateEquipment: (id, updates) => {
        set((state) => ({
          equipment: state.equipment.map((eq) =>
            eq.id === id
              ? { ...eq, ...updates, metadata: { ...eq.metadata, updatedAt: new Date() } }
              : eq
          ),
        }));
      },

      removeEquipment: (id) => {
        set((state) => ({
          equipment: state.equipment.filter((eq) => eq.id !== id),
          selectedObjects: state.selectedObjects.filter((objId) => objId !== id),
        }));
      },

      // Selection operations
      selectObject: (id) => {
        set({ selectedObjects: [id] });
      },

      selectMultiple: (ids) => {
        set({ selectedObjects: ids });
      },

      clearSelection: () => {
        set({ selectedObjects: [] });
      },

      toggleSelection: (id) => {
        set((state) => ({
          selectedObjects: state.selectedObjects.includes(id)
            ? state.selectedObjects.filter((objId) => objId !== id)
            : [...state.selectedObjects, id],
        }));
      },

      // Conversion operations
      convertStickLineToDuct: (stickLineId, ductProperties) => {
        const state = get();
        const stickLine = state.stickLines.find((line) => line.id === stickLineId);
        
        if (!stickLine) return;

        // Create duct from stick line
        const duct: Omit<Duct3D, 'id' | 'metadata'> = {
          geometry: {
            type: 'rectangular',
            width: 12,
            height: 8,
            length: stickLine.points.length > 1 
              ? stickLine.points[0].distanceTo(stickLine.points[stickLine.points.length - 1])
              : 1,
          },
          position: stickLine.points[0] || new THREE.Vector3(0, 0, 0),
          rotation: new THREE.Euler(0, 0, 0),
          material: {
            type: 'galvanized-steel',
            color: '#c0c0c0',
            roughness: 0.3,
          },
          isSelected: false,
          properties: {
            airflow: stickLine.metadata.airflow || 1000,
            velocity: 1200,
            pressureLoss: 0.1,
            ductType: stickLine.metadata.ductType || 'supply',
          },
          ...ductProperties,
        };

        // Add the duct
        get().addDuct({
          ...duct,
          metadata: {
            sourceStickLineId: stickLineId,
          },
        });

        // Optionally remove the stick line
        // get().removeStickLine(stickLineId);
      },

      // View operations
      setViewMode: (mode) => set({ viewMode: mode }),
      toggleGrid: () => set((state) => ({ gridVisible: !state.gridVisible })),
      toggleSnapToGrid: () => set((state) => ({ snapToGrid: !state.snapToGrid })),
      setGridSize: (size) => set({ gridSize: size }),

      // Canvas operations
      setCanvasSize: (width, height) => set({ canvasSize: { width, height } }),
      setCameraPosition: (position) => set({ cameraPosition: position }),
      setCameraTarget: (target) => set({ cameraTarget: target }),

      // Utility operations
      clearAll: () => set({
        stickLines: [],
        ducts: [],
        equipment: [],
        selectedObjects: [],
      }),

      getObjectById: (id) => {
        const state = get();
        return (
          state.stickLines.find((obj) => obj.id === id) ||
          state.ducts.find((obj) => obj.id === id) ||
          state.equipment.find((obj) => obj.id === id) ||
          null
        );
      },

      getSelectedObjects: () => {
        const state = get();
        return state.selectedObjects
          .map((id) => state.getObjectById(id))
          .filter(Boolean) as (StickLine | Duct3D | Equipment3D)[];
      },
    }),
    {
      name: 'canvas-3d-store',
    }
  )
);
