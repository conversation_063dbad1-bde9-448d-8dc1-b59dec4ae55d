/**
 * SizeWise Suite - Project Properties Panel
 * 
 * Retractable panel (top-left) with 5 sections as specified in requirements:
 * 1. Info - Project details
 * 2. Codes - Building codes and standards
 * 3. Defaults - Default values and settings
 * 4. Team - Team members and permissions
 * 5. Admin - Administrative settings
 */

'use client';

import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface ProjectPropertiesPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

type PanelSection = 'info' | 'codes' | 'defaults' | 'team' | 'admin';

export const ProjectPropertiesPanel: React.FC<ProjectPropertiesPanelProps> = ({
  isOpen,
  onToggle,
  className = '',
}) => {
  const [activeSection, setActiveSection] = useState<PanelSection>('info');

  const sections = [
    { id: 'info' as PanelSection, label: 'Info', icon: '📋' },
    { id: 'codes' as PanelSection, label: 'Codes', icon: '📖' },
    { id: 'defaults' as PanelSection, label: 'Defaults', icon: '⚙️' },
    { id: 'team' as PanelSection, label: 'Team', icon: '👥' },
    { id: 'admin' as PanelSection, label: 'Admin', icon: '🔧' },
  ];

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'info':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Project Name
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                placeholder="Enter project name"
                defaultValue="HVAC System Design"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                rows={3}
                placeholder="Project description"
                defaultValue="Commercial office building HVAC ductwork design and sizing"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Location
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50"
                placeholder="Project location"
                defaultValue="New York, NY"
              />
            </div>
          </div>
        );
      
      case 'codes':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Building Code
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50">
                <option>IBC 2021</option>
                <option>IBC 2018</option>
                <option>IBC 2015</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                ASHRAE Standard
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white/50 dark:bg-gray-800/50">
                <option>ASHRAE 90.1-2019</option>
                <option>ASHRAE 90.1-2016</option>
                <option>ASHRAE 62.1-2019</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                SMACNA Standards
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">HVAC Duct Construction Standards</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" className="mr-2" defaultChecked />
                  <span className="text-sm">Duct Design Guidelines</span>
                </label>
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">🚧</div>
            <p>{activeSection.charAt(0).toUpperCase() + activeSection.slice(1)} section</p>
            <p className="text-sm">Coming soon...</p>
          </div>
        );
    }
  };

  return (
    <div className={`flex ${className}`}>
      {/* Panel Content */}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isOpen ? 'w-80 opacity-100' : 'w-0 opacity-0'
        } overflow-hidden`}
      >
        <div className="h-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-lg shadow-lg border border-gray-200/50 dark:border-gray-700/50">
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Project Properties
            </h3>
            
            {/* Section Tabs */}
            <div className="flex space-x-1 mb-4 bg-gray-100/50 dark:bg-gray-800/50 rounded-lg p-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`flex-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                    activeSection === section.id
                      ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                  }`}
                >
                  <div className="flex flex-col items-center">
                    <span className="text-sm mb-1">{section.icon}</span>
                    <span>{section.label}</span>
                  </div>
                </button>
              ))}
            </div>
            
            {/* Section Content */}
            <div className="max-h-96 overflow-y-auto">
              {renderSectionContent()}
            </div>
          </div>
        </div>
      </div>
      
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="ml-2 w-8 h-12 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-r-lg flex items-center justify-center hover:bg-white/90 dark:hover:bg-gray-800/90 transition-colors"
      >
        {isOpen ? (
          <ChevronLeftIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <ChevronRightIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        )}
      </button>
    </div>
  );
};

export default ProjectPropertiesPanel;
