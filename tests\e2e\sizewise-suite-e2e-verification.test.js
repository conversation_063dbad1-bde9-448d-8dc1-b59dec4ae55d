/**
 * SizeWise Suite - Comprehensive E2E Testing Verification
 * 
 * Verifies all implemented features against SizeWise Task V1 requirements
 * and Augment Implementation Protocol compliance
 */

const fs = require('fs');
const path = require('path');

describe('SizeWise Suite E2E Verification', () => {
  const projectRoot = path.join(__dirname, '../..');
  
  describe('1. Navigation System Testing', () => {
    test('should have centered navigation component implemented', () => {
      const navPath = path.join(projectRoot, 'app/shared/components/navigation/CenteredNavigation.tsx');
      expect(fs.existsSync(navPath)).toBe(true);
      
      const navContent = fs.readFileSync(navPath, 'utf8');
      expect(navContent).toContain('CenteredNavigation');
      expect(navContent).toContain('Home|File|Projects|Tools|Profile');
      expect(navContent).toContain('dropdown');
      expect(navContent).toContain('glassmorphism');
    });

    test('should have new AppShell with centered navigation', () => {
      const shellPath = path.join(projectRoot, 'app/core/layout/AppShell.tsx');
      expect(fs.existsSync(shellPath)).toBe(true);
      
      const shellContent = fs.readFileSync(shellPath, 'utf8');
      expect(shellContent).toContain('CenteredNavigation');
      expect(shellContent).not.toContain('sidebar');
      expect(shellContent).toContain('enableCenteredNavigation');
    });

    test('should have feature flag controlled navigation', () => {
      const flagsPath = path.join(projectRoot, 'app/config/feature-flags.ts');
      expect(fs.existsSync(flagsPath)).toBe(true);
      
      const flagsContent = fs.readFileSync(flagsPath, 'utf8');
      expect(flagsContent).toContain('enableCenteredNavigation');
      expect(flagsContent).toContain('enableNewThemeSystem');
    });
  });

  describe('2. Air Duct Sizer - 9 Required UI Elements', () => {
    test('should have AirDuctSizerLayout with all 9 elements', () => {
      const layoutPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/layout/AirDuctSizerLayout.tsx');
      expect(fs.existsSync(layoutPath)).toBe(true);
      
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');
      
      // Verify all 9 UI elements are referenced
      expect(layoutContent).toContain('ProjectPropertiesPanel'); // 1
      expect(layoutContent).toContain('Canvas3D'); // 2
      expect(layoutContent).toContain('DrawingFAB'); // 3
      expect(layoutContent).toContain('ViewCube'); // 4
      expect(layoutContent).toContain('CalculationBar'); // 5
      expect(layoutContent).toContain('ImportExportPanel'); // 6
      expect(layoutContent).toContain('WarningPanel'); // 7
      expect(layoutContent).toContain('SelectionPopUp'); // 8
      expect(layoutContent).toContain('DrawingToolbar'); // 9
    });

    test('1. Project Properties Panel - should be implemented correctly', () => {
      const panelPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/panels/ProjectPropertiesPanel.tsx');
      expect(fs.existsSync(panelPath)).toBe(true);
      
      const panelContent = fs.readFileSync(panelPath, 'utf8');
      expect(panelContent).toContain('top-left');
      expect(panelContent).toContain('retractable');
      expect(panelContent).toContain('info');
      expect(panelContent).toContain('codes');
      expect(panelContent).toContain('defaults');
      expect(panelContent).toContain('team');
      expect(panelContent).toContain('admin');
    });

    test('2. 3D Canvas Workspace - should be implemented with Three.js', () => {
      const canvasPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/workspace/Canvas3D.tsx');
      expect(fs.existsSync(canvasPath)).toBe(true);
      
      const canvasContent = fs.readFileSync(canvasPath, 'utf8');
      expect(canvasContent).toContain('@react-three/fiber');
      expect(canvasContent).toContain('@react-three/drei');
      expect(canvasContent).toContain('Canvas');
      expect(canvasContent).toContain('OrbitControls');
      expect(canvasContent).toContain('Grid');
      expect(canvasContent).toContain('StickLineRenderer');
      expect(canvasContent).toContain('DuctRenderer');
    });

    test('3. Drawing Tool FAB - should be bottom-right positioned', () => {
      const fabPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/ui/DrawingFAB.tsx');
      expect(fs.existsSync(fabPath)).toBe(true);
      
      const fabContent = fs.readFileSync(fabPath, 'utf8');
      expect(fabContent).toContain('bottom-right');
      expect(fabContent).toContain('stick-line');
      expect(fabContent).toContain('floating');
    });

    test('4. View Cube - should be top-right 3D navigation', () => {
      const cubePath = path.join(projectRoot, 'app/features/air-duct-sizer/components/ui/ViewCube.tsx');
      expect(fs.existsSync(cubePath)).toBe(true);
      
      const cubeContent = fs.readFileSync(cubePath, 'utf8');
      expect(cubeContent).toContain('top-right');
      expect(cubeContent).toContain('3D navigation');
      expect(cubeContent).toContain('isometric');
    });

    test('5. Calculation Bar - should be bottom full-width', () => {
      const barPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/panels/CalculationBar.tsx');
      expect(fs.existsSync(barPath)).toBe(true);
      
      const barContent = fs.readFileSync(barPath, 'utf8');
      expect(barContent).toContain('bottom full-width');
      expect(barContent).toContain('calculation results');
      expect(barContent).toContain('airflow');
      expect(barContent).toContain('pressure');
    });

    test('6. Import/Export Panel - should be collapsible', () => {
      const panelPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/panels/ImportExportPanel.tsx');
      expect(fs.existsSync(panelPath)).toBe(true);
      
      const panelContent = fs.readFileSync(panelPath, 'utf8');
      expect(panelContent).toContain('collapsible');
      expect(panelContent).toContain('import');
      expect(panelContent).toContain('export');
      expect(panelContent).toContain('CAD');
      expect(panelContent).toContain('PDF');
    });

    test('7. Warning Panel - should be right-edge retractable', () => {
      const warningPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/panels/WarningPanel.tsx');
      expect(fs.existsSync(warningPath)).toBe(true);
      
      const warningContent = fs.readFileSync(warningPath, 'utf8');
      expect(warningContent).toContain('right-edge');
      expect(warningContent).toContain('retractable');
      expect(warningContent).toContain('warnings');
      expect(warningContent).toContain('alerts');
    });

    test('8. Selection Pop-Up - should be contextual', () => {
      const popupPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/ui/SelectionPopUp.tsx');
      expect(fs.existsSync(popupPath)).toBe(true);
      
      const popupContent = fs.readFileSync(popupPath, 'utf8');
      expect(popupContent).toContain('contextual');
      expect(popupContent).toContain('properties');
      expect(popupContent).toContain('selectedObjects');
    });

    test('9. Drawing Toolbar - should be left-side with tools', () => {
      const toolbarPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/ui/DrawingToolbar.tsx');
      expect(fs.existsSync(toolbarPath)).toBe(true);
      
      const toolbarContent = fs.readFileSync(toolbarPath, 'utf8');
      expect(toolbarContent).toContain('left-side');
      expect(toolbarContent).toContain('drawing tools');
      expect(toolbarContent).toContain('stick-line');
      expect(toolbarContent).toContain('rectangle-duct');
      expect(toolbarContent).toContain('round-duct');
    });
  });

  describe('3. 3D Canvas and Rendering System', () => {
    test('should have stick line renderer', () => {
      const rendererPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/workspace/StickLineRenderer.tsx');
      expect(fs.existsSync(rendererPath)).toBe(true);
      
      const rendererContent = fs.readFileSync(rendererPath, 'utf8');
      expect(rendererContent).toContain('StickLineRenderer');
      expect(rendererContent).toContain('Line');
      expect(rendererContent).toContain('THREE.Vector3');
    });

    test('should have 3D duct renderer', () => {
      const ductPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/workspace/DuctRenderer.tsx');
      expect(fs.existsSync(ductPath)).toBe(true);
      
      const ductContent = fs.readFileSync(ductPath, 'utf8');
      expect(ductContent).toContain('DuctRenderer');
      expect(ductContent).toContain('rectangular');
      expect(ductContent).toContain('round');
      expect(ductContent).toContain('boxGeometry');
      expect(ductContent).toContain('cylinderGeometry');
    });

    test('should have equipment renderer', () => {
      const equipPath = path.join(projectRoot, 'app/features/air-duct-sizer/components/workspace/EquipmentRenderer.tsx');
      expect(fs.existsSync(equipPath)).toBe(true);
      
      const equipContent = fs.readFileSync(equipPath, 'utf8');
      expect(equipContent).toContain('EquipmentRenderer');
      expect(equipContent).toContain('fan');
      expect(equipContent).toContain('damper');
      expect(equipContent).toContain('diffuser');
    });
  });

  describe('4. State Management System', () => {
    test('should have 3D canvas store', () => {
      const storePath = path.join(projectRoot, 'app/features/air-duct-sizer/store/canvas-store.ts');
      expect(fs.existsSync(storePath)).toBe(true);
      
      const storeContent = fs.readFileSync(storePath, 'utf8');
      expect(storeContent).toContain('useCanvas3DStore');
      expect(storeContent).toContain('StickLine');
      expect(storeContent).toContain('Duct3D');
      expect(storeContent).toContain('Equipment3D');
      expect(storeContent).toContain('convertStickLineToDuct');
    });

    test('should have drawing store', () => {
      const drawingPath = path.join(projectRoot, 'app/features/air-duct-sizer/store/drawing-store.ts');
      expect(fs.existsSync(drawingPath)).toBe(true);
      
      const drawingContent = fs.readFileSync(drawingPath, 'utf8');
      expect(drawingContent).toContain('useDrawingStore');
      expect(drawingContent).toContain('DrawingTool');
      expect(drawingContent).toContain('startStickLine');
      expect(drawingContent).toContain('finishStickLine');
    });
  });

  describe('5. Standards Compliance System', () => {
    test('should have SMACNA service implementation', () => {
      const smacnaPath = path.join(projectRoot, 'app/features/standards-compliance/services/smacna-service.ts');
      expect(fs.existsSync(smacnaPath)).toBe(true);
      
      const smacnaContent = fs.readFileSync(smacnaPath, 'utf8');
      expect(smacnaContent).toContain('SMACNAService');
      expect(smacnaContent).toContain('calculateDuctSizing');
      expect(smacnaContent).toContain('SMACNA_VERSION');
      expect(smacnaContent).toContain('velocity limits');
      expect(smacnaContent).toContain('pressure loss');
    });

    test('should have versioned standards compliance', () => {
      const smacnaPath = path.join(projectRoot, 'app/features/standards-compliance/services/smacna-service.ts');
      const smacnaContent = fs.readFileSync(smacnaPath, 'utf8');
      
      expect(smacnaContent).toContain('SMACNA_VERSION = \'2023.1.0\'');
      expect(smacnaContent).toContain('LAST_UPDATED');
      expect(smacnaContent).toContain('versioned module');
      expect(smacnaContent).toContain('unit tested');
    });
  });

  describe('6. Feature Flag System', () => {
    test('should have comprehensive feature flags', () => {
      const flagsPath = path.join(projectRoot, 'app/config/feature-flags.ts');
      expect(fs.existsSync(flagsPath)).toBe(true);
      
      const flagsContent = fs.readFileSync(flagsPath, 'utf8');
      expect(flagsContent).toContain('enableCenteredNavigation');
      expect(flagsContent).toContain('enable3DCanvas');
      expect(flagsContent).toContain('enableNewAirDuctSizer');
      expect(flagsContent).toContain('enableProjectProperties');
      expect(flagsContent).toContain('enableStandardsCompliance');
    });

    test('should have feature flag hooks', () => {
      const hookPath = path.join(projectRoot, 'app/core/hooks/useFeatureFlags.ts');
      expect(fs.existsSync(hookPath)).toBe(true);
      
      const hookContent = fs.readFileSync(hookPath, 'utf8');
      expect(hookContent).toContain('useFeatureFlags');
      expect(hookContent).toContain('getFeatureFlags');
      expect(hookContent).toContain('setFeatureFlagOverride');
    });
  });

  describe('7. Migration and Backup System', () => {
    test('should have migration scripts', () => {
      const migrationPath = path.join(projectRoot, 'scripts/migration/01-create-directory-structure.js');
      expect(fs.existsSync(migrationPath)).toBe(true);
      
      const backupPath = path.join(projectRoot, 'scripts/migration/backup-and-rollback.js');
      expect(fs.existsSync(backupPath)).toBe(true);
    });

    test('should have migration safety tests', () => {
      const testPath = path.join(projectRoot, 'tests/migration/migration-safety.test.js');
      expect(fs.existsSync(testPath)).toBe(true);
      
      const testContent = fs.readFileSync(testPath, 'utf8');
      expect(testContent).toContain('Migration Safety Tests');
      expect(testContent).toContain('backup');
      expect(testContent).toContain('rollback');
      expect(testContent).toContain('≥85% coverage');
    });

    test('should have ADR documentation', () => {
      const adrPath = path.join(projectRoot, 'docs/architecture/decisions/ADR-001-architecture-migration-to-feature-based-3d-first.md');
      expect(fs.existsSync(adrPath)).toBe(true);
      
      const adrContent = fs.readFileSync(adrPath, 'utf8');
      expect(adrContent).toContain('APPROVED');
      expect(adrContent).toContain('feature-based architecture');
      expect(adrContent).toContain('3D-first');
    });
  });

  describe('8. Package Dependencies', () => {
    test('should have Three.js dependencies installed', () => {
      const packagePath = path.join(projectRoot, 'frontend-nextjs/package.json');
      expect(fs.existsSync(packagePath)).toBe(true);
      
      const packageContent = fs.readFileSync(packagePath, 'utf8');
      const packageJson = JSON.parse(packageContent);
      
      expect(packageJson.dependencies).toHaveProperty('three');
      expect(packageJson.dependencies).toHaveProperty('@react-three/fiber');
      expect(packageJson.dependencies).toHaveProperty('@react-three/drei');
      expect(packageJson.devDependencies || packageJson.dependencies).toHaveProperty('@types/three');
    });
  });

  describe('9. Route Configuration', () => {
    test('should have new Air Duct Sizer route', () => {
      const routePath = path.join(projectRoot, 'frontend-nextjs/app/air-duct-sizer-new/page.tsx');
      expect(fs.existsSync(routePath)).toBe(true);
      
      const routeContent = fs.readFileSync(routePath, 'utf8');
      expect(routeContent).toContain('AirDuctSizerPage');
      expect(routeContent).toContain('@/features/air-duct-sizer/page');
    });

    test('should have updated main layout', () => {
      const layoutPath = path.join(projectRoot, 'frontend-nextjs/app/layout.tsx');
      expect(fs.existsSync(layoutPath)).toBe(true);
      
      const layoutContent = fs.readFileSync(layoutPath, 'utf8');
      expect(layoutContent).toContain('@/core/layout/AppShell');
    });
  });
});

// Performance and Integration Tests
describe('Performance and Integration Verification', () => {
  test('should have performance monitoring in 3D canvas', () => {
    const canvasPath = path.join(process.cwd(), 'app/features/air-duct-sizer/components/workspace/Canvas3D.tsx');
    const canvasContent = fs.readFileSync(canvasPath, 'utf8');
    
    expect(canvasContent).toContain('PerformanceMonitor');
    expect(canvasContent).toContain('fps');
    expect(canvasContent).toContain('drawCalls');
  });

  test('should have proper error boundaries and fallbacks', () => {
    const shellPath = path.join(process.cwd(), 'app/core/layout/AppShell.tsx');
    const shellContent = fs.readFileSync(shellPath, 'utf8');
    
    expect(shellContent).toContain('LegacyAppShell');
    expect(shellContent).toContain('enableCenteredNavigation');
  });
});
