/**
 * SizeWise Suite - Centered Navigation
 * 
 * Replaces sidebar navigation with centered top navigation
 * Required by SizeWise Task V1 specifications
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface NavItem {
  label: string;
  href?: string;
  dropdown?: {
    label: string;
    href: string;
    description?: string;
  }[];
}

const navigationItems: NavItem[] = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'File',
    dropdown: [
      { label: 'New Project', href: '/projects/new', description: 'Create a new HVAC project' },
      { label: 'Open Project', href: '/projects', description: 'Open an existing project' },
      { label: 'Recent Projects', href: '/projects/recent', description: 'Access recently opened projects' },
      { label: 'Import', href: '/import', description: 'Import CAD files or data' },
      { label: 'Export', href: '/export', description: 'Export project data' },
    ],
  },
  {
    label: 'Projects',
    dropdown: [
      { label: 'All Projects', href: '/projects', description: 'View all projects' },
      { label: 'My Projects', href: '/projects/mine', description: 'Projects you own' },
      { label: 'Shared Projects', href: '/projects/shared', description: 'Projects shared with you' },
      { label: 'Templates', href: '/projects/templates', description: 'Project templates' },
    ],
  },
  {
    label: 'Tools',
    dropdown: [
      { label: 'Air Duct Sizer', href: '/air-duct-sizer-new', description: 'Size HVAC ductwork' },
      { label: 'Load Calculator', href: '/tools/load-calculator', description: 'Calculate heating/cooling loads' },
      { label: 'Equipment Selector', href: '/tools/equipment', description: 'Select HVAC equipment' },
      { label: 'Standards Compliance', href: '/tools/standards', description: 'Check code compliance' },
    ],
  },
  {
    label: 'Profile',
    dropdown: [
      { label: 'Account Settings', href: '/settings/account', description: 'Manage your account' },
      { label: 'Preferences', href: '/settings/preferences', description: 'App preferences' },
      { label: 'Team Management', href: '/settings/team', description: 'Manage team members' },
      { label: 'Billing', href: '/settings/billing', description: 'Subscription and billing' },
      { label: 'Sign Out', href: '/auth/signout', description: 'Sign out of your account' },
    ],
  },
];

export const CenteredNavigation: React.FC = () => {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const handleDropdownToggle = (label: string) => {
    setActiveDropdown(activeDropdown === label ? null : label);
  };

  const handleDropdownClose = () => {
    setActiveDropdown(null);
  };

  return (
    <nav className="w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SW</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white">SizeWise Suite</span>
            </Link>
          </div>

          {/* Centered Navigation Items */}
          <div className="hidden md:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div key={item.label} className="relative">
                {item.href ? (
                  <Link
                    href={item.href}
                    className="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    {item.label}
                  </Link>
                ) : (
                  <button
                    onClick={() => handleDropdownToggle(item.label)}
                    className="flex items-center space-x-1 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    <span>{item.label}</span>
                    <ChevronDownIcon
                      className={`w-4 h-4 transition-transform ${
                        activeDropdown === item.label ? 'rotate-180' : ''
                      }`}
                    />
                  </button>
                )}

                {/* Dropdown Menu */}
                {item.dropdown && activeDropdown === item.label && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-lg shadow-lg border border-gray-200/50 dark:border-gray-700/50 py-2 z-50">
                    {item.dropdown.map((dropdownItem) => (
                      <Link
                        key={dropdownItem.href}
                        href={dropdownItem.href}
                        onClick={handleDropdownClose}
                        className="block px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100/50 dark:hover:bg-gray-700/50 transition-colors"
                      >
                        <div className="font-medium">{dropdownItem.label}</div>
                        {dropdownItem.description && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {dropdownItem.description}
                          </div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right side - Theme toggle and user menu */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>

            {/* User Avatar */}
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">U</span>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu overlay */}
      {activeDropdown && (
        <div
          className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm md:hidden"
          onClick={handleDropdownClose}
        />
      )}
    </nav>
  );
};
