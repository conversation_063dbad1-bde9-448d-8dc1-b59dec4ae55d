/**
 * SizeWise Suite - Air Duct Sizer Page
 * 
 * Main page for the Air Duct Sizer tool with 3D workspace
 * Implements all 9 required UI elements as specified in SizeWise Task V1
 */

'use client';

import React from 'react';
import { AirDuctSizerLayout } from './components/layout/AirDuctSizerLayout';
import { AppErrorBoundary } from '../../shared/components/error-boundaries/AppErrorBoundary';

export default function AirDuctSizerPage() {
  return (
    <AppErrorBoundary>
      <div className="w-full h-screen">
        <AirDuctSizerLayout />
      </div>
    </AppErrorBoundary>
  );
}
