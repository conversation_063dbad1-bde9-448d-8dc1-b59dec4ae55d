#!/usr/bin/env node
/**
 * SizeWise Suite - Backup and Rollback Utilities
 * 
 * Provides comprehensive backup and rollback capabilities
 * Required by Augment Implementation Protocol
 * 
 * SAFETY: All operations are logged and reversible
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BackupManager {
  constructor() {
    this.backupDir = path.join(process.cwd(), 'backups');
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.currentBackup = path.join(this.backupDir, `backup-${this.timestamp}`);
  }

  /**
   * Create comprehensive backup before migration
   */
  async createBackup() {
    console.log('🔄 Creating comprehensive backup...');
    
    // Ensure backup directory exists
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
    
    if (!fs.existsSync(this.currentBackup)) {
      fs.mkdirSync(this.currentBackup, { recursive: true });
    }

    try {
      // 1. Git state backup
      await this.backupGitState();
      
      // 2. Source code backup
      await this.backupSourceCode();
      
      // 3. Configuration backup
      await this.backupConfiguration();
      
      // 4. User data backup (if any local storage)
      await this.backupUserData();
      
      // 5. Dependencies backup
      await this.backupDependencies();
      
      // 6. Create backup manifest
      await this.createBackupManifest();
      
      console.log(`✅ Backup completed: ${this.currentBackup}`);
      return this.currentBackup;
      
    } catch (error) {
      console.error('❌ Backup failed:', error);
      throw error;
    }
  }

  async backupGitState() {
    console.log('📦 Backing up Git state...');
    
    const gitDir = path.join(this.currentBackup, 'git');
    fs.mkdirSync(gitDir, { recursive: true });
    
    // Current branch and commit
    const branch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    const commit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
    const status = execSync('git status --porcelain', { encoding: 'utf8' });
    
    fs.writeFileSync(path.join(gitDir, 'branch.txt'), branch);
    fs.writeFileSync(path.join(gitDir, 'commit.txt'), commit);
    fs.writeFileSync(path.join(gitDir, 'status.txt'), status);
    
    // Create patch of uncommitted changes
    if (status.trim()) {
      const diff = execSync('git diff HEAD', { encoding: 'utf8' });
      fs.writeFileSync(path.join(gitDir, 'uncommitted.patch'), diff);
    }
    
    console.log(`   Branch: ${branch}`);
    console.log(`   Commit: ${commit.substring(0, 8)}`);
  }

  async backupSourceCode() {
    console.log('📁 Backing up source code...');
    
    const sourceDir = path.join(this.currentBackup, 'source');
    
    // Copy critical directories
    const criticalDirs = [
      'frontend-nextjs/app',
      'frontend-nextjs/components',
      'frontend-nextjs/stores',
      'frontend-nextjs/types',
      'docs',
      'scripts',
    ];
    
    criticalDirs.forEach(dir => {
      const srcPath = path.join(process.cwd(), dir);
      const destPath = path.join(sourceDir, dir);
      
      if (fs.existsSync(srcPath)) {
        this.copyDirectory(srcPath, destPath);
        console.log(`   ✅ ${dir}`);
      }
    });
  }

  async backupConfiguration() {
    console.log('⚙️  Backing up configuration...');
    
    const configDir = path.join(this.currentBackup, 'config');
    fs.mkdirSync(configDir, { recursive: true });
    
    const configFiles = [
      'package.json',
      'package-lock.json',
      'next.config.js',
      'tsconfig.json',
      'tailwind.config.js',
      '.env.local',
      '.env.example',
    ];
    
    configFiles.forEach(file => {
      const srcPath = path.join(process.cwd(), 'frontend-nextjs', file);
      const destPath = path.join(configDir, file);
      
      if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, destPath);
        console.log(`   ✅ ${file}`);
      }
    });
  }

  async backupUserData() {
    console.log('👤 Backing up user data...');
    
    // For now, this is placeholder for future local storage backup
    const userDataDir = path.join(this.currentBackup, 'user-data');
    fs.mkdirSync(userDataDir, { recursive: true });
    
    // Create placeholder for future implementation
    fs.writeFileSync(
      path.join(userDataDir, 'README.md'),
      '# User Data Backup\n\nThis directory will contain user data backups when local storage is implemented.\n'
    );
  }

  async backupDependencies() {
    console.log('📦 Backing up dependencies...');
    
    const depsDir = path.join(this.currentBackup, 'dependencies');
    fs.mkdirSync(depsDir, { recursive: true });
    
    // Copy lock files and package.json
    const depFiles = ['package.json', 'package-lock.json'];
    
    depFiles.forEach(file => {
      const srcPath = path.join(process.cwd(), 'frontend-nextjs', file);
      const destPath = path.join(depsDir, file);
      
      if (fs.existsSync(srcPath)) {
        fs.copyFileSync(srcPath, destPath);
      }
    });
    
    // Save npm list output
    try {
      const npmList = execSync('cd frontend-nextjs && npm list --depth=0', { encoding: 'utf8' });
      fs.writeFileSync(path.join(depsDir, 'npm-list.txt'), npmList);
    } catch (error) {
      console.log('   ⚠️  Could not generate npm list');
    }
  }

  async createBackupManifest() {
    const manifest = {
      timestamp: this.timestamp,
      date: new Date().toISOString(),
      version: require('../../frontend-nextjs/package.json').version,
      git: {
        branch: execSync('git branch --show-current', { encoding: 'utf8' }).trim(),
        commit: execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim(),
        hasUncommitted: execSync('git status --porcelain', { encoding: 'utf8' }).trim() !== '',
      },
      migration: {
        phase: 'pre-migration',
        purpose: 'Architecture migration to feature-based 3D-first system',
        adr: 'ADR-001',
      },
      rollback: {
        instructions: 'Run: node scripts/migration/backup-and-rollback.js --rollback ' + this.timestamp,
        automated: true,
        tested: false, // Will be updated after rollback testing
      }
    };
    
    fs.writeFileSync(
      path.join(this.currentBackup, 'manifest.json'),
      JSON.stringify(manifest, null, 2)
    );
    
    console.log('📋 Backup manifest created');
  }

  /**
   * Rollback to a specific backup
   */
  async rollback(backupTimestamp) {
    console.log(`🔄 Rolling back to backup: ${backupTimestamp}`);
    
    const backupPath = path.join(this.backupDir, `backup-${backupTimestamp}`);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup not found: ${backupPath}`);
    }
    
    try {
      // 1. Restore Git state
      await this.restoreGitState(backupPath);
      
      // 2. Restore source code
      await this.restoreSourceCode(backupPath);
      
      // 3. Restore configuration
      await this.restoreConfiguration(backupPath);
      
      // 4. Restore dependencies
      await this.restoreDependencies(backupPath);
      
      console.log('✅ Rollback completed successfully');
      
    } catch (error) {
      console.error('❌ Rollback failed:', error);
      throw error;
    }
  }

  async restoreGitState(backupPath) {
    console.log('📦 Restoring Git state...');
    
    const gitDir = path.join(backupPath, 'git');
    
    if (fs.existsSync(path.join(gitDir, 'commit.txt'))) {
      const commit = fs.readFileSync(path.join(gitDir, 'commit.txt'), 'utf8').trim();
      execSync(`git checkout ${commit}`);
      console.log(`   Restored to commit: ${commit.substring(0, 8)}`);
    }
    
    // Apply uncommitted changes if they exist
    const patchFile = path.join(gitDir, 'uncommitted.patch');
    if (fs.existsSync(patchFile)) {
      try {
        execSync(`git apply ${patchFile}`);
        console.log('   Restored uncommitted changes');
      } catch (error) {
        console.log('   ⚠️  Could not apply uncommitted changes patch');
      }
    }
  }

  async restoreSourceCode(backupPath) {
    console.log('📁 Restoring source code...');
    
    const sourceDir = path.join(backupPath, 'source');
    
    // Remove current directories and restore from backup
    const criticalDirs = [
      'frontend-nextjs/app',
      'frontend-nextjs/components',
      'frontend-nextjs/stores',
      'frontend-nextjs/types',
    ];
    
    criticalDirs.forEach(dir => {
      const currentPath = path.join(process.cwd(), dir);
      const backupDirPath = path.join(sourceDir, dir);
      
      if (fs.existsSync(backupDirPath)) {
        // Remove current directory
        if (fs.existsSync(currentPath)) {
          fs.rmSync(currentPath, { recursive: true, force: true });
        }
        
        // Restore from backup
        this.copyDirectory(backupDirPath, currentPath);
        console.log(`   ✅ Restored ${dir}`);
      }
    });
  }

  async restoreConfiguration(backupPath) {
    console.log('⚙️  Restoring configuration...');
    
    const configDir = path.join(backupPath, 'config');
    
    const configFiles = [
      'package.json',
      'package-lock.json',
      'next.config.js',
      'tsconfig.json',
      'tailwind.config.js',
    ];
    
    configFiles.forEach(file => {
      const backupFile = path.join(configDir, file);
      const currentFile = path.join(process.cwd(), 'frontend-nextjs', file);
      
      if (fs.existsSync(backupFile)) {
        fs.copyFileSync(backupFile, currentFile);
        console.log(`   ✅ Restored ${file}`);
      }
    });
  }

  async restoreDependencies(backupPath) {
    console.log('📦 Restoring dependencies...');
    
    // Dependencies are restored with configuration
    // Run npm install to ensure consistency
    try {
      execSync('cd frontend-nextjs && npm install', { stdio: 'inherit' });
      console.log('   ✅ Dependencies reinstalled');
    } catch (error) {
      console.log('   ⚠️  Manual npm install may be required');
    }
  }

  // Utility method to copy directories recursively
  copyDirectory(src, dest) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    
    const entries = fs.readdirSync(src, { withFileTypes: true });
    
    entries.forEach(entry => {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        this.copyDirectory(srcPath, destPath);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  /**
   * List available backups
   */
  listBackups() {
    if (!fs.existsSync(this.backupDir)) {
      console.log('No backups found');
      return [];
    }
    
    const backups = fs.readdirSync(this.backupDir)
      .filter(name => name.startsWith('backup-'))
      .map(name => {
        const backupPath = path.join(this.backupDir, name);
        const manifestPath = path.join(backupPath, 'manifest.json');
        
        let manifest = {};
        if (fs.existsSync(manifestPath)) {
          manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        }
        
        return {
          name,
          timestamp: name.replace('backup-', ''),
          path: backupPath,
          manifest,
        };
      })
      .sort((a, b) => b.timestamp.localeCompare(a.timestamp));
    
    return backups;
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const backupManager = new BackupManager();
  
  if (args[0] === '--rollback' && args[1]) {
    backupManager.rollback(args[1])
      .then(() => console.log('🎉 Rollback completed'))
      .catch(error => {
        console.error('❌ Rollback failed:', error);
        process.exit(1);
      });
  } else if (args[0] === '--list') {
    const backups = backupManager.listBackups();
    console.log('📋 Available backups:');
    backups.forEach(backup => {
      console.log(`   ${backup.timestamp} - ${backup.manifest.date || 'Unknown date'}`);
    });
  } else {
    backupManager.createBackup()
      .then(backupPath => console.log(`🎉 Backup created: ${backupPath}`))
      .catch(error => {
        console.error('❌ Backup failed:', error);
        process.exit(1);
      });
  }
}

module.exports = BackupManager;
