/**
 * SizeWise Suite - Drawing Tool FAB (Floating Action Button)
 * 
 * Bottom-right toggle for drawing stick lines
 */

'use client';

import React, { useState } from 'react';
import { PencilIcon, CursorArrowRaysIcon } from '@heroicons/react/24/outline';

interface DrawingFABProps {
  className?: string;
}

export const DrawingFAB: React.FC<DrawingFABProps> = ({ className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTool, setActiveTool] = useState('select');

  const tools = [
    { id: 'select', icon: CursorArrowRaysIcon, label: 'Select', color: 'bg-gray-500' },
    { id: 'stick-line', icon: PencilIcon, label: 'Draw Stick Line', color: 'bg-blue-500' },
  ];

  const activeToolData = tools.find(tool => tool.id === activeTool) || tools[0];

  return (
    <div className={`${className}`}>
      {isExpanded && (
        <div className="mb-3 space-y-2">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => {
                setActiveTool(tool.id);
                setIsExpanded(false);
              }}
              className={`w-12 h-12 rounded-full ${tool.color} text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center ${
                activeTool === tool.id ? 'ring-4 ring-white/50' : ''
              }`}
              title={tool.label}
            >
              <tool.icon className="w-6 h-6" />
            </button>
          ))}
        </div>
      )}

      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-14 h-14 rounded-full ${activeToolData.color} text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center ${
          isExpanded ? 'rotate-45' : ''
        }`}
        title={isExpanded ? 'Close' : activeToolData.label}
      >
        {isExpanded ? (
          <div className="w-6 h-0.5 bg-white"></div>
        ) : (
          <activeToolData.icon className="w-7 h-7" />
        )}
      </button>
    </div>
  );
};

export default DrawingFAB;
