{"timestamp": "2025-07-20T21:13:01.066Z", "summary": {"passed": 3, "failed": 3, "successRate": "50.0"}, "tests": [{"description": "Main application loads correctly", "status": "❌ FAIL", "error": "Expected title to contain 'SizeWise', got: "}, {"description": "Centered navigation is present and functional", "status": "❌ FAIL", "error": "locator.waitFor: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('nav') to be visible\u001b[22m\n"}, {"description": "All 5 navigation items are present", "status": "❌ FAIL", "error": "locator.waitFor: Timeout 5000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('text=Home').first() to be visible\u001b[22m\n"}, {"description": "Air Duct Sizer page loads directly", "status": "✅ PASS"}, {"description": "No critical module resolution errors", "status": "✅ PASS"}, {"description": "Application loads within reasonable time", "status": "✅ PASS"}], "screenshots": ["test-results/main-application.png", "test-results/centered-navigation.png", "test-results/air-duct-sizer-page.png"]}