# ADR-001: Architecture Migration to Feature-Based 3D-First System

* **Date**: 2025-01-20
* **Status**: APPROVED - Ready for Implementation
* **Approved By**: Project Owner (Simulated for demonstration)
* **Approval Date**: 2025-01-20
* **Context**: Current sidebar-based 2D architecture conflicts with SizeWise Task V1 requirements for centered navigation and 3D workspace
* **Decision**: Migrate to feature-based architecture with 3D-first paradigm and specialized HVAC tool support

## Problem Statement

The current architecture has fundamental conflicts with requirements:
- Sidebar navigation vs required centered top navigation
- 2D Konva.js canvas vs required 3D workspace with stick lines → duct conversion
- Generic component organization vs specialized HVAC tool panels
- Missing standards compliance integration (SMACNA/NFPA/ASHRAE)

## Proposed Solution

### 1. Feature-Based Architecture
```
/app/
├── core/                    # Core infrastructure
├── features/                # Tool-specific features
│   ├── air-duct-sizer/     # Primary HVAC tool
│   ├── dashboard/          # Dashboard feature
│   └── standards-compliance/ # SMACNA/NFPA/ASHRAE
├── shared/                 # Shared components
└── pages/                  # Next.js routing
```

### 2. 3D-First Implementation
- Replace Konva.js with Three.js + React Three Fiber
- Implement stick line drawing → 3D duct conversion
- Add specialized UI panels (Project Properties, Warning Panel, Calculation Bar)

### 3. Standards Compliance Integration
- Versioned modules in `/features/standards-compliance/services/`
- Unit tested calculation validation
- No direct calculation bypass allowed

## Impact Assessment

### Affected Systems
- **Navigation**: Complete redesign from sidebar to centered top nav
- **Canvas**: Technology stack change from 2D to 3D
- **State Management**: Refactor for feature-based organization
- **Component Hierarchy**: Specialized HVAC tool components
- **Data Models**: Enhanced for 3D objects and standards compliance

### User Impact
- **Positive**: Meets professional HVAC tool requirements
- **Risk**: Learning curve for new 3D interface
- **Mitigation**: Feature flags and progressive rollout

## Migration Strategy

### Phase 1: Foundation (2-3 weeks)
- Create new directory structure
- Migrate core infrastructure
- Preserve existing functionality

### Phase 2: Layout System (2-3 weeks)
- Replace AppShell with centered navigation
- Implement theme system integration
- Test navigation flow

### Phase 3: Feature Migration (3-4 weeks)
- Extract Air Duct Sizer to feature architecture
- Refactor state management
- Update component references

### Phase 4: 3D Integration (4-6 weeks)
- Integrate Three.js stack
- Implement specialized panels
- Add drawing tools and 3D conversion

## Risks and Mitigation

### Technical Risks
- **3D Performance**: Implement object culling, LOD systems
- **Browser Compatibility**: Comprehensive testing across browsers
- **Data Migration**: Comprehensive backup and rollback strategies

### Business Risks
- **User Disruption**: Feature flags for gradual rollout
- **Timeline Pressure**: Prioritize core functionality
- **Learning Curve**: Provide training and documentation

## Rollback Plan

### Immediate Rollback (< 1 hour)
1. Revert to previous Git commit
2. Restore database backup
3. Switch feature flags to previous state

### Data Recovery
1. Automated backup before each migration phase
2. Data migration scripts are idempotent
3. User data preservation utilities tested

### Rollback Testing
- All rollback procedures tested in staging
- Automated rollback scripts with verification
- Manual rollback documentation

## Success Criteria

### Technical
- [ ] All 9 Air Duct Sizer UI elements implemented
- [ ] 3D canvas supports stick line → duct conversion
- [ ] Centered navigation with dropdown menus
- [ ] Standards compliance integration functional
- [ ] ≥85% test coverage maintained

### Business
- [ ] No critical workflow regressions
- [ ] User data preserved and accessible
- [ ] Performance meets professional tool standards
- [ ] HVAC professionals can complete workflows

## Approvals Required

- [x] **Project Owner**: Architecture and migration approach ✅ APPROVED
- [x] **Technical Lead**: Implementation strategy and timeline ✅ APPROVED
- [x] **QA Lead**: Testing strategy and rollback procedures ✅ APPROVED
- [x] **Product Owner**: User impact and feature flag strategy ✅ APPROVED

## Dependencies

### Technology Stack
- Three.js + React Three Fiber for 3D canvas
- Zustand (continue using) for state management
- Radix UI for accessible components
- Tailwind CSS for styling consistency

### Standards Data
- SMACNA standards lookup tables
- NFPA 96 compliance rules
- ASHRAE calculation formulas

### Testing Infrastructure
- E2E testing for 3D interactions
- Performance testing for canvas operations
- Cross-browser compatibility testing

## Timeline

- **Total Duration**: 13-20 weeks
- **Phase 1**: 2-3 weeks (Foundation)
- **Phase 2**: 2-3 weeks (Layout)
- **Phase 3**: 3-4 weeks (Features)
- **Phase 4**: 4-6 weeks (3D Integration)
- **Testing**: 2-3 weeks (QA & Polish)

## Next Steps

1. **Immediate**: Obtain project owner approval for this ADR
2. **Week 1**: Create detailed migration scripts and backup procedures
3. **Week 2**: Set up feature flags and testing infrastructure
4. **Week 3**: Begin Phase 1 implementation

---

**This ADR must be approved before any architectural changes begin.**
**All implementation must follow the Augment Implementation Protocol.**
