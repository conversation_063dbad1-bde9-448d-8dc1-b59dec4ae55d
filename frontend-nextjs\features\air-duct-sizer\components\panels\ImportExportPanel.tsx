/**
 * SizeWise Suite - Import/Export Panel
 * 
 * Collapsible panel above calculation bar for import/export operations
 */

'use client';

import React from 'react';
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

interface ImportExportPanelProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const ImportExportPanel: React.FC<ImportExportPanelProps> = ({
  isOpen,
  onToggle,
}) => {
  return (
    <div className="w-full bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm border-t border-gray-200/50 dark:border-gray-700/50">
      <button
        onClick={onToggle}
        className="w-full px-6 py-2 flex items-center justify-between hover:bg-white/50 dark:hover:bg-gray-800/50 transition-colors"
      >
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Import / Export
        </span>
        {isOpen ? (
          <ChevronDownIcon className="w-4 h-4 text-gray-500" />
        ) : (
          <ChevronUpIcon className="w-4 h-4 text-gray-500" />
        )}
      </button>

      <div
        className={`transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen ? 'max-h-40 opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="px-6 pb-4">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Import</h4>
              <div className="space-y-2">
                <button className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                  📄 Import CAD Plan
                </button>
                <button className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                  📋 Import CSV Data
                </button>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Export</h4>
              <div className="space-y-2">
                <button className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors">
                  📄 Export PDF Report
                </button>
                <button className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors">
                  📊 Export Excel Data
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImportExportPanel;
