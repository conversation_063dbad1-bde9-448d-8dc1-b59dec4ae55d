---
type: "always_apply"
---

# 📦 SizeWise Suite Architecture & Implementation Protocol (v2)

---

## 🧭 Purpose

This protocol governs all architecture, development, and migration for the SizeWise Suite.  
All contributions—AI or human—must be:

- **Implementation-ready**
- **Consistent with feature-centric, 3D-first design**
- **Strictly aligned with project folder rules and standards compliance**
- **Fully auditable with rationale and rollback plans**

---

## 1. Architectural Assessment: Current vs. Required

### ✅ **Preservable Elements**
- Next.js framework (App Router)
- Zustand for state management
- TypeScript-first codebase
- Glassmorphism UI components
- Core data models (project/room/segment)

### ❌ **Critical Incompatibilities**
| Current                  | Required                   | Conflict      |
|--------------------------|----------------------------|---------------|
| Sidebar navigation       | Centered top navigation    | 🔴 Critical   |
| 2D Konva.js canvas       | 3D Three.js workspace      | 🔴 Critical   |
| Flat/generic UI          | Specialized tool panels    | 🔴 Critical   |
| Mobile-first layout      | Desktop-first layout       | 🟡 Major      |
| Page-based routing       | Tool-centric navigation    | 🟡 Major      |

---

## 2. Directory & Folder Structure

> **All modules must follow the structure below. No restructuring or re-architecture may proceed without written technical justification, project owner approval, and a rollback plan.**

```

/app/
├── core/
│   ├── layout/           # AppShell, CenteredNavigation, ThemeProvider
│   ├── state/            # Zustand/global state stores
│   ├── services/         # API, standards, calculation engines
│   └── types/            # Shared TypeScript types
│
├── features/             # Each major tool = one feature folder
│   ├── dashboard/
│   ├── air-duct-sizer/
│   │   ├── components/
│   │   │   ├── workspace/Canvas3D/
│   │   │   ├── panels/
│   │   │   ├── ui/
│   │   │   └── layout/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── store/
│   │   └── types/
│   ├── project-management/
│   ├── standards-compliance/
│   └── future-tools/
│
├── shared/
│   ├── components/
│   ├── hooks/
│   ├── utils/
│   └── constants/
│
└── pages/                # Thin Next.js routing only

```

- **Each feature folder is fully self-contained.**
- **Shared code only in `/shared/` or `/core/`—never duplicated.**
- **All standards logic (SMACNA/NFPA) as versioned, testable modules in `/features/standards-compliance/services/`.**

---

## 3. Implementation Phases

| Phase      | Duration     | Deliverables                                              |
|------------|-------------|-----------------------------------------------------------|
| Foundation | 2–3 weeks   | Directory restructure, infra migration, configs updated   |
| Layout     | 2–3 weeks   | Centered navigation, AppShell, theme system               |
| Features   | 3–4 weeks   | Feature extraction, state refactor, reference updates     |
| 3D Canvas  | 4–6 weeks   | Three.js integration, panel/workspace system              |
| QA/Polish  | 2–3 weeks   | Regression/E2E tests, optimization, bug fixes             |

---

## 4. Migration & Data Preservation

- **Write migration utilities:** Convert 2D project data to new 3D models.
- **Backup and rollback scripts:** Must be created and tested before any destructive migration.
- **No migration proceeds without full documentation and test coverage.**

---

## 5. Enforcement & Security Clauses

> ### ❗️**Codebase Restructure Protection**
>
> - No contributor (AI, agent, or human) is permitted to propose or execute any codebase restructure, re-architecture, or directory refactor without:
>     - **A written, explicit, technical justification (scope, risk, benefit)**
>     - **Written project owner approval, stored as an ADR in `/docs/architecture/decisions/`**
>     - **A documented rollback and migration plan**
> - Any unapproved restructure must be reverted immediately.

---

## 6. Required Folder Enforcement Table

| Folder                                | Permitted Files | Purpose                                    | Migration Notes            |
|----------------------------------------|----------------|---------------------------------------------|---------------------------|
| `/core/layout/`                        | .tsx           | CenteredNav, AppShell, ThemeProvider        | Replace sidebars           |
| `/features/air-duct-sizer/components/workspace/Canvas3D/` | .tsx | 3D drawing workspace                    | New Three.js-based         |
| `/features/*/services/standards/`      | .ts            | Standards logic (SMACNA, NFPA)              | Must be versioned/tested   |
| `/shared/components/panels/`           | .tsx           | UI panel system (reusable panels)           | For all tools              |
| `/core/state/`                         | .ts            | Zustand/global state                        | No other state solution    |

---

## 7. Required Additions to Prior Protocols

- **3D-first mandate:** All spatial tools and visualizations must use 3D (Three.js or equivalent).
- **Feature folder self-containment:** Each tool module must be independently testable and upgradable.
- **Feature flag gating:** All major UI or engine rewrites must be behind a toggle for at least one release.
- **Migration/data preservation enforcement:** Every schema or feature migration must include conversion scripts and rollback.
- **Centralized, versioned standards logic:** All compliance logic is shared and tracked as a versioned module.

---

## 8. Success Metrics

- **100% standards compliance** for all calculation and export outputs.
- **No critical regressions** in workflows or data after migration.
- **>85% unit test coverage** on all new code and all migrated features.
- **Full documentation** for every migration, feature, and structural change.

---

## 9. Review & Rationale Requirement

- **Every architectural change must be justified:**  
  Include a rationale in all PRs—why the change is required, what requirement it meets, and its impact.
- **ADR Documentation:**  
  All major decisions stored in `/docs/architecture/decisions/` as Markdown ADRs.

---

## 10. Final Checklist (For Any Contribution)

- [ ] All code and docs follow this structure and rules.
- [ ] All migrations have scripts, test coverage, and rollback steps.
- [ ] No structure or architecture changes without owner-approved ADR.
- [ ] All standards logic and shared components are centralized.
- [ ] Each feature module is independently testable.
- [ ] Documentation and i18n updated for all user-facing text.
- [ ] Summary of changes, tests to run, and next steps are provided.

---

> **This document supersedes previous architecture and folder guidelines.  
> Non-compliance results in reversion and process review.**