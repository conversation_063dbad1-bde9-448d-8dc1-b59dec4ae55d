/**
 * SizeWise Suite - 3D Canvas Component
 * 
 * Main 3D workspace for the Air Duct Sizer
 * Integrates Three.js with React Three Fiber for professional HVAC design
 */

'use client';

import React, { Suspense, useRef, useEffect, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Grid, PerformanceMonitor } from '@react-three/drei';
import * as THREE from 'three';
import { AppErrorBoundary } from '../../../../shared/components/error-boundaries/AppErrorBoundary';
import { LoadingFallback } from '../../../../shared/components/loading/LoadingFallback';

// Import renderers with error handling
const StickLineRenderer = React.lazy(() => 
  import('./StickLineRenderer').catch(() => ({ 
    default: () => <group /> // Fallback empty group
  }))
);

const DuctRenderer = React.lazy(() => 
  import('./DuctRenderer').catch(() => ({ 
    default: () => <group /> // Fallback empty group
  }))
);

const EquipmentRenderer = React.lazy(() => 
  import('./EquipmentRenderer').catch(() => ({ 
    default: () => <group /> // Fallback empty group
  }))
);

const DrawingTools = React.lazy(() => 
  import('./DrawingTools').catch(() => ({ 
    default: () => null // Fallback null component
  }))
);

interface Canvas3DProps {
  width?: number;
  height?: number;
  className?: string;
}

export const Canvas3D: React.FC<Canvas3DProps> = ({
  width,
  height,
  className = '',
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [performanceData, setPerformanceData] = useState({ fps: 60, drawCalls: 0 });
  const [isWebGLSupported, setIsWebGLSupported] = useState(true);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const [isMounted, setIsMounted] = useState(false);

  // Initialize component and check WebGL support
  useEffect(() => {
    setIsMounted(true);

    // Set dimensions from props or use defaults
    const finalWidth = width || (typeof window !== 'undefined' ? window.innerWidth : 800);
    const finalHeight = height || (typeof window !== 'undefined' ? window.innerHeight : 600);
    setDimensions({ width: finalWidth, height: finalHeight });

    // Check WebGL support
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) {
      setIsWebGLSupported(false);
      console.warn('WebGL not supported, falling back to 2D canvas');
    }
  }, [width, height]);

  // Performance monitoring
  const handlePerformanceChange = (data: { fps: number; factor: number }) => {
    setPerformanceData(prev => ({
      ...prev,
      fps: Math.round(data.fps),
    }));

    // Warn if performance is poor
    if (data.fps < 30) {
      console.warn('Low FPS detected:', data.fps);
    }
  };

  // Don't render until mounted to avoid hydration mismatch
  if (!isMounted) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 ${className}`} style={{ width: 800, height: 600 }}>
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading 3D workspace...</p>
        </div>
      </div>
    );
  }

  // WebGL not supported fallback
  if (!isWebGLSupported) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 ${className}`}>
        <div className="text-center p-8">
          <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
            3D Graphics Not Supported
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Your browser doesn't support WebGL, which is required for the 3D workspace.
          </p>
          <p className="text-sm text-gray-400 dark:text-gray-500">
            Please use a modern browser like Chrome, Firefox, or Safari.
          </p>
        </div>
      </div>
    );
  }

  return (
    <AppErrorBoundary
      fallback={
        <div className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 ${className}`}>
          <LoadingFallback message="Loading 3D workspace..." />
        </div>
      }
    >
      <div className={`relative ${className}`} style={{ width: dimensions.width, height: dimensions.height }}>
        <Canvas
          ref={canvasRef}
          camera={{ 
            position: [10, 10, 10], 
            fov: 75,
            near: 0.1,
            far: 1000
          }}
          gl={{
            antialias: true,
            alpha: true,
            powerPreference: "high-performance",
          }}
          shadows
          onCreated={({ gl }) => {
            gl.toneMapping = THREE.ACESFilmicToneMapping;
            gl.toneMappingExposure = 1;
            gl.shadowMap.enabled = true;
            gl.shadowMap.type = THREE.PCFSoftShadowMap;
          }}
        >
          {/* Performance monitoring */}
          <PerformanceMonitor onIncline={handlePerformanceChange} onDecline={handlePerformanceChange} />
          
          {/* Lighting setup */}
          <ambientLight intensity={0.4} />
          <directionalLight
            position={[10, 10, 5]}
            intensity={1}
            castShadow
            shadow-mapSize-width={2048}
            shadow-mapSize-height={2048}
            shadow-camera-far={50}
            shadow-camera-left={-10}
            shadow-camera-right={10}
            shadow-camera-top={10}
            shadow-camera-bottom={-10}
          />
          
          {/* Grid and helpers */}
          <Grid
            args={[20, 20]}
            cellSize={1}
            cellThickness={0.5}
            cellColor="#6b7280"
            sectionSize={5}
            sectionThickness={1}
            sectionColor="#374151"
            fadeDistance={25}
            fadeStrength={1}
            followCamera={false}
            infiniteGrid={true}
          />
          
          {/* 3D Object Renderers */}
          <Suspense fallback={null}>
            <StickLineRenderer 
              stickLines={[]}
              currentStickLine={null}
              selectedObjects={[]}
            />
            <DuctRenderer 
              ducts={[]}
              selectedObjects={[]}
              viewMode="solid"
            />
            <EquipmentRenderer 
              equipment={[]}
              selectedObjects={[]}
            />
          </Suspense>
          
          {/* Camera controls */}
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            dampingFactor={0.05}
            screenSpacePanning={false}
            minDistance={1}
            maxDistance={100}
            maxPolarAngle={Math.PI / 2}
          />
        </Canvas>
        
        {/* Drawing tools overlay */}
        <Suspense fallback={null}>
          <DrawingTools />
        </Suspense>
        
        {/* Performance indicator (development only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="absolute top-2 left-2 bg-black/80 text-white text-xs px-2 py-1 rounded z-10">
            FPS: {performanceData.fps}
          </div>
        )}
        
        {/* 3D Canvas ready indicator */}
        <div className="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400">
          3D Workspace Ready
        </div>
      </div>
    </AppErrorBoundary>
  );
};
