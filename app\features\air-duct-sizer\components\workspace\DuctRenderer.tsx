/**
 * SizeWise Suite - Duct Renderer
 * 
 * Renders 3D ducts converted from stick lines
 * Part of the 3D-first implementation
 */

'use client';

import React from 'react';
import * as THREE from 'three';
import type { Duct3D, ViewMode } from '../../store/canvas-store';

interface DuctRendererProps {
  ducts: Duct3D[];
  selectedObjects: string[];
  viewMode: ViewMode;
}

export const DuctRenderer: React.FC<DuctRendererProps> = ({
  ducts,
  selectedObjects,
  viewMode,
}) => {
  const getMaterial = (duct: Duct3D, isSelected: boolean) => {
    const baseColor = isSelected ? '#ff6b6b' : duct.material.color;
    
    switch (viewMode) {
      case 'wireframe':
        return <meshBasicMaterial color={baseColor} wireframe />;
      case 'transparent':
        return <meshStandardMaterial color={baseColor} transparent opacity={0.7} />;
      case 'xray':
        return <meshBasicMaterial color={baseColor} transparent opacity={0.3} />;
      default:
        return <meshStandardMaterial color={baseColor} roughness={duct.material.roughness} />;
    }
  };

  const renderRectangularDuct = (duct: Duct3D) => {
    const isSelected = selectedObjects.includes(duct.id);
    const { width = 12, height = 8, length } = duct.geometry;
    
    // Convert inches to feet for 3D display
    const widthFt = width / 12;
    const heightFt = height / 12;
    const lengthFt = length / 12;
    
    return (
      <mesh
        key={duct.id}
        position={[duct.position.x, duct.position.y, duct.position.z]}
        rotation={[duct.rotation.x, duct.rotation.y, duct.rotation.z]}
      >
        <boxGeometry args={[widthFt, heightFt, lengthFt]} />
        {getMaterial(duct, isSelected)}
        
        {/* Wireframe overlay for selected ducts */}
        {isSelected && (
          <mesh>
            <boxGeometry args={[widthFt, heightFt, lengthFt]} />
            <meshBasicMaterial color="#ff6b6b" wireframe />
          </mesh>
        )}
      </mesh>
    );
  };

  const renderRoundDuct = (duct: Duct3D) => {
    const isSelected = selectedObjects.includes(duct.id);
    const { diameter = 10, length } = duct.geometry;
    
    // Convert inches to feet for 3D display
    const radiusFt = diameter / 24; // diameter to radius, inches to feet
    const lengthFt = length / 12;
    
    return (
      <mesh
        key={duct.id}
        position={[duct.position.x, duct.position.y, duct.position.z]}
        rotation={[duct.rotation.x, duct.rotation.y, duct.rotation.z]}
      >
        <cylinderGeometry args={[radiusFt, radiusFt, lengthFt, 16]} />
        {getMaterial(duct, isSelected)}
        
        {/* Wireframe overlay for selected ducts */}
        {isSelected && (
          <mesh>
            <cylinderGeometry args={[radiusFt, radiusFt, lengthFt, 16]} />
            <meshBasicMaterial color="#ff6b6b" wireframe />
          </mesh>
        )}
      </mesh>
    );
  };

  return (
    <group>
      {ducts.map((duct) => {
        if (duct.geometry.type === 'rectangular') {
          return renderRectangularDuct(duct);
        } else {
          return renderRoundDuct(duct);
        }
      })}
    </group>
  );
};
