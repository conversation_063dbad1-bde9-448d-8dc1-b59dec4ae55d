/**
 * SizeWise Suite - View Cube
 * 
 * Top-right 3D navigation aid for camera control
 */

'use client';

import React from 'react';

interface ViewCubeProps {
  className?: string;
}

export const ViewCube: React.FC<ViewCubeProps> = ({ className = '' }) => {
  const handleViewChange = (view: string) => {
    console.log(`Changing view to: ${view}`);
  };

  return (
    <div className={`${className}`}>
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-3 shadow-lg border border-gray-200/50 dark:border-gray-700/50">
        <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
          View Cube
        </div>
        
        <div className="relative w-16 h-16 mx-auto">
          <button
            onClick={() => handleViewChange('top')}
            className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-4 bg-blue-200 hover:bg-blue-300 border border-blue-400 text-xs font-medium text-blue-800 flex items-center justify-center transition-colors"
            title="Top View"
          >
            T
          </button>
          
          <button
            onClick={() => handleViewChange('front')}
            className="absolute top-4 left-4 w-8 h-8 bg-gray-200 hover:bg-gray-300 border border-gray-400 text-xs font-medium text-gray-800 flex items-center justify-center transition-colors"
            title="Front View"
          >
            F
          </button>
        </div>
        
        <div className="mt-3 space-y-1">
          <button
            onClick={() => handleViewChange('isometric')}
            className="w-full px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Isometric
          </button>
          <button
            onClick={() => handleViewChange('home')}
            className="w-full px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Home View
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewCube;
