/**
 * SizeWise Suite - Warning Panel
 * 
 * Right-edge retractable panel for displaying warnings and alerts
 * Part of the 9 required UI elements for Air Duct Sizer
 */

'use client';

import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { GlassCard } from '@/shared/components/glassmorphism/GlassCard';

interface WarningPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

interface Warning {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  details?: string;
  timestamp: Date;
}

const mockWarnings: Warning[] = [
  {
    id: '1',
    type: 'warning',
    message: 'Velocity exceeds SMACNA recommendations',
    details: 'Duct section has velocity of 2100 FPM, exceeding the recommended maximum of 1800 FPM for supply ducts.',
    timestamp: new Date(),
  },
  {
    id: '2',
    type: 'error',
    message: 'Pressure loss calculation failed',
    details: 'Unable to calculate pressure loss for rectangular duct due to invalid dimensions.',
    timestamp: new Date(),
  },
  {
    id: '3',
    type: 'info',
    message: 'Standards compliance check complete',
    details: 'All duct sections have been validated against SMACNA standards.',
    timestamp: new Date(),
  },
];

export const WarningPanel: React.FC<WarningPanelProps> = ({
  isOpen,
  onToggle,
  className = '',
}) => {
  const getWarningIcon = (type: Warning['type']) => {
    switch (type) {
      case 'error':
        return '🔴';
      case 'warning':
        return '🟡';
      case 'info':
        return '🔵';
      default:
        return '⚪';
    }
  };

  const getWarningColor = (type: Warning['type']) => {
    switch (type) {
      case 'error':
        return 'text-red-600 dark:text-red-400';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'info':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <div className={`flex ${className}`}>
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="mr-2 w-8 h-12 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-l-lg flex items-center justify-center hover:bg-white/90 dark:hover:bg-gray-800/90 transition-colors"
      >
        {isOpen ? (
          <ChevronRightIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        ) : (
          <ChevronLeftIcon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        )}
      </button>

      {/* Panel Content */}
      <div
        className={`transition-all duration-300 ease-in-out ${
          isOpen ? 'w-80 opacity-100' : 'w-0 opacity-0'
        } overflow-hidden`}
      >
        <GlassCard className="h-full">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                <ExclamationTriangleIcon className="w-5 h-5 mr-2 text-yellow-500" />
                Warnings & Alerts
              </h3>
              <span className="text-sm text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                {mockWarnings.length}
              </span>
            </div>
            
            {/* Warning List */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {mockWarnings.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">✅</div>
                  <p>No warnings or alerts</p>
                  <p className="text-sm">All systems are operating normally</p>
                </div>
              ) : (
                mockWarnings.map((warning) => (
                  <div
                    key={warning.id}
                    className="p-3 bg-white/30 dark:bg-gray-800/30 rounded-lg border-l-4 border-l-yellow-400"
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-lg flex-shrink-0 mt-0.5">
                        {getWarningIcon(warning.type)}
                      </span>
                      <div className="flex-1 min-w-0">
                        <div className={`font-medium text-sm ${getWarningColor(warning.type)}`}>
                          {warning.message}
                        </div>
                        {warning.details && (
                          <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                            {warning.details}
                          </div>
                        )}
                        <div className="text-xs text-gray-500 mt-2">
                          {warning.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>

            {/* Actions */}
            {mockWarnings.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
                <div className="flex space-x-2">
                  <button className="flex-1 px-3 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                    Fix All
                  </button>
                  <button className="flex-1 px-3 py-2 text-sm bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                    Clear All
                  </button>
                </div>
              </div>
            )}
          </div>
        </GlassCard>
      </div>
    </div>
  );
};
