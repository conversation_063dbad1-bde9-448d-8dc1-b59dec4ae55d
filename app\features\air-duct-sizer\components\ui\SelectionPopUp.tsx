/**
 * SizeWise Suite - Selection Pop-Up
 * 
 * Contextual window for editing properties of selected objects
 * Part of the 9 required UI elements for Air Duct Sizer
 */

'use client';

import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { GlassCard } from '@/shared/components/glassmorphism/GlassCard';

interface SelectionPopUpProps {
  position: { x: number; y: number };
  selectedObjects: string[];
  onClose: () => void;
  className?: string;
}

export const SelectionPopUp: React.FC<SelectionPopUpProps> = ({
  position,
  selectedObjects,
  onClose,
  className = '',
}) => {
  // Mock object data - in real implementation, this would come from the canvas store
  const mockObjectData = {
    type: 'duct',
    properties: {
      width: 12,
      height: 8,
      length: 10,
      airflow: 1200,
      velocity: 1150,
      material: 'galvanized-steel',
    },
  };

  return (
    <div
      className={`${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)',
      }}
    >
      <GlassCard className="w-64 p-4 shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-800 dark:text-white">
            {selectedObjects.length === 1 ? 'Object Properties' : `${selectedObjects.length} Objects Selected`}
          </h4>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <XMarkIcon className="w-4 h-4 text-gray-500" />
          </button>
        </div>

        {/* Properties */}
        {selectedObjects.length === 1 ? (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Type
              </label>
              <div className="text-sm text-gray-800 dark:text-white capitalize">
                {mockObjectData.type}
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Width
                </label>
                <input
                  type="number"
                  value={mockObjectData.properties.width}
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Height
                </label>
                <input
                  type="number"
                  value={mockObjectData.properties.height}
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                />
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Length
              </label>
              <input
                type="number"
                value={mockObjectData.properties.length}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Airflow (CFM)
              </label>
              <input
                type="number"
                value={mockObjectData.properties.airflow}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Material
              </label>
              <select className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50">
                <option value="galvanized-steel">Galvanized Steel</option>
                <option value="stainless-steel">Stainless Steel</option>
                <option value="aluminum">Aluminum</option>
                <option value="fiberglass">Fiberglass</option>
              </select>
            </div>

            {/* Calculated Properties */}
            <div className="pt-2 border-t border-gray-200/50 dark:border-gray-700/50">
              <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
                Calculated Properties
              </div>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Velocity:</span>
                  <span className="font-medium">{mockObjectData.properties.velocity} FPM</span>
                </div>
                <div className="flex justify-between">
                  <span>Area:</span>
                  <span className="font-medium">{(mockObjectData.properties.width * mockObjectData.properties.height / 144).toFixed(2)} sq ft</span>
                </div>
                <div className="flex justify-between">
                  <span>Perimeter:</span>
                  <span className="font-medium">{((mockObjectData.properties.width + mockObjectData.properties.height) * 2 / 12).toFixed(2)} ft</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Multiple objects selected. Bulk edit options:
            </div>
            <div className="space-y-2">
              <button className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                Change Material
              </button>
              <button className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                Apply Sizing
              </button>
              <button className="w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                Delete Selected
              </button>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-2 mt-4 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
          <button className="flex-1 px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
            Apply
          </button>
          <button
            onClick={onClose}
            className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Cancel
          </button>
        </div>
      </GlassCard>
    </div>
  );
};
