/**
 * SizeWise Suite - New App Shell with Centered Navigation
 * 
 * Replaces the sidebar-based AppShell with centered navigation
 * Implements the layout system required by SizeWise Task V1
 * 
 * Features:
 * - Centered top navigation (no sidebar)
 * - Desktop-first design
 * - Theme system integration
 * - Feature flag controlled rollout
 */

'use client';

import React, { useEffect } from 'react';
import { CenteredNavigation } from '@/shared/components/navigation/CenteredNavigation';
import { useFeatureFlags } from '@/core/hooks/useFeatureFlags';
import { useTheme } from '@/core/hooks/useTheme';

// Import the old AppShell as fallback
import { AppShell as LegacyAppShell } from '@/frontend-nextjs/components/layout/AppShell';

interface AppShellProps {
  children: React.ReactNode;
}

export const AppShell: React.FC<AppShellProps> = ({ children }) => {
  const { enableCenteredNavigation, enableNewThemeSystem } = useFeatureFlags();
  const { theme, setTheme } = useTheme();

  // Apply theme to document
  useEffect(() => {
    if (enableNewThemeSystem) {
      const root = document.documentElement;
      
      if (theme === 'dark') {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
      
      // Apply theme-specific CSS variables
      if (theme === 'light') {
        root.style.setProperty('--background', '255 255 255');
        root.style.setProperty('--foreground', '0 0 0');
      } else if (theme === 'dark') {
        root.style.setProperty('--background', '0 0 0');
        root.style.setProperty('--foreground', '255 255 255');
      }
    }
  }, [theme, enableNewThemeSystem]);

  // Feature flag check - use legacy AppShell if new navigation is disabled
  if (!enableCenteredNavigation) {
    return <LegacyAppShell>{children}</LegacyAppShell>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      {/* Centered Navigation */}
      <CenteredNavigation />
      
      {/* Main Content Area */}
      <main className="flex-1">
        <div className="w-full h-full">
          {children}
        </div>
      </main>
      
      {/* Global overlays and modals would go here */}
      <div id="modal-root" />
      <div id="toast-root" />
    </div>
  );
};

export default AppShell;
