/**
 * SizeWise Suite - Feature Flags Hook
 * 
 * Provides access to feature flags for progressive rollout
 * Required by Augment Implementation Protocol
 * 
 * All major changes must be behind feature flags
 */

'use client';

import { useState, useEffect } from 'react';
import { getFeatureFlags, type FeatureFlags } from '@/config/feature-flags';

export const useFeatureFlags = (): FeatureFlags => {
  const [flags, setFlags] = useState<FeatureFlags>(getFeatureFlags());

  useEffect(() => {
    // In a real implementation, this would subscribe to feature flag updates
    // from a service like LaunchDarkly, Split.io, or a custom backend
    
    // For now, we'll just use the static configuration
    setFlags(getFeatureFlags());
    
    // Listen for environment changes or manual overrides
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'sizewise-feature-flags') {
        try {
          const overrides = JSON.parse(e.newValue || '{}');
          setFlags(prev => ({ ...prev, ...overrides }));
        } catch (error) {
          console.warn('Invalid feature flags in localStorage:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    // Check for localStorage overrides on mount
    try {
      const stored = localStorage.getItem('sizewise-feature-flags');
      if (stored) {
        const overrides = JSON.parse(stored);
        setFlags(prev => ({ ...prev, ...overrides }));
      }
    } catch (error) {
      console.warn('Invalid feature flags in localStorage:', error);
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return flags;
};

// Utility function to override feature flags (for development/testing)
export const setFeatureFlagOverride = (flagName: keyof FeatureFlags, value: boolean) => {
  try {
    const stored = localStorage.getItem('sizewise-feature-flags');
    const overrides = stored ? JSON.parse(stored) : {};
    overrides[flagName] = value;
    localStorage.setItem('sizewise-feature-flags', JSON.stringify(overrides));
    
    // Trigger storage event for other tabs/components
    window.dispatchEvent(new StorageEvent('storage', {
      key: 'sizewise-feature-flags',
      newValue: JSON.stringify(overrides),
    }));
  } catch (error) {
    console.error('Failed to set feature flag override:', error);
  }
};

// Utility function to clear all overrides
export const clearFeatureFlagOverrides = () => {
  localStorage.removeItem('sizewise-feature-flags');
  window.dispatchEvent(new StorageEvent('storage', {
    key: 'sizewise-feature-flags',
    newValue: null,
  }));
};
