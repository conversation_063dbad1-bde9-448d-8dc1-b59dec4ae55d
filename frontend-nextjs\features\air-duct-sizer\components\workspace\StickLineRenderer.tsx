/**
 * SizeWise Suite - Stick Line Renderer
 * 
 * Renders stick lines in 3D space for duct layout planning
 * Part of the 3D-first implementation
 */

'use client';

import React from 'react';
import { Line } from '@react-three/drei';
import * as THREE from 'three';

export interface StickLine {
  id: string;
  points: THREE.Vector3[];
  color: string;
  thickness: number;
  isSelected: boolean;
  metadata: {
    airflow?: number;
    ductType?: 'supply' | 'return' | 'exhaust';
    createdAt: Date;
    updatedAt: Date;
  };
}

interface StickLineRendererProps {
  stickLines: StickLine[];
  currentStickLine: THREE.Vector3[] | null;
  selectedObjects: string[];
}

export const StickLineRenderer: React.FC<StickLineRendererProps> = ({
  stickLines,
  currentStickLine,
  selectedObjects,
}) => {
  return (
    <group name="stick-lines">
      {/* Render existing stick lines */}
      {stickLines.map((stickLine) => {
        const isSelected = selectedObjects.includes(stickLine.id);
        const color = isSelected ? '#ff6b6b' : stickLine.color;
        const lineWidth = isSelected ? stickLine.thickness * 2 : stickLine.thickness;
        
        return (
          <Line
            key={stickLine.id}
            points={stickLine.points}
            color={color}
            lineWidth={lineWidth}
            dashed={false}
          />
        );
      })}
      
      {/* Render current drawing stick line */}
      {currentStickLine && currentStickLine.length > 1 && (
        <Line
          points={currentStickLine}
          color="#3b82f6"
          lineWidth={3}
          dashed={true}
          dashSize={0.5}
          gapSize={0.3}
        />
      )}
      
      {/* Demo stick line for testing */}
      {stickLines.length === 0 && !currentStickLine && (
        <Line
          points={[
            new THREE.Vector3(0, 0, 0),
            new THREE.Vector3(5, 0, 0),
            new THREE.Vector3(5, 3, 0),
            new THREE.Vector3(10, 3, 0),
          ]}
          color="#3b82f6"
          lineWidth={2}
          dashed={false}
        />
      )}
    </group>
  );
};

export default StickLineRenderer;
