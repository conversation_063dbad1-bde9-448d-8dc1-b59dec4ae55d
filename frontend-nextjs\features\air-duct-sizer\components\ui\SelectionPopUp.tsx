/**
 * SizeWise Suite - Selection Pop-Up
 * 
 * Contextual window for editing properties of selected objects
 */

'use client';

import React from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface SelectionPopUpProps {
  position: { x: number; y: number };
  selectedObjects: string[];
  onClose: () => void;
  className?: string;
}

export const SelectionPopUp: React.FC<SelectionPopUpProps> = ({
  position,
  selectedObjects,
  onClose,
  className = '',
}) => {
  return (
    <div
      className={`${className}`}
      style={{
        left: position.x,
        top: position.y,
        transform: 'translate(-50%, -100%)',
      }}
    >
      <div className="w-64 p-4 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-lg shadow-xl border border-gray-200/50 dark:border-gray-700/50">
        <div className="flex items-center justify-between mb-3">
          <h4 className="font-medium text-gray-800 dark:text-white">
            {selectedObjects.length === 1 ? 'Object Properties' : `${selectedObjects.length} Objects Selected`}
          </h4>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
          >
            <XMarkIcon className="w-4 h-4 text-gray-500" />
          </button>
        </div>

        {selectedObjects.length === 1 ? (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Type
              </label>
              <div className="text-sm text-gray-800 dark:text-white">Duct</div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Width
                </label>
                <input
                  type="number"
                  defaultValue={12}
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Height
                </label>
                <input
                  type="number"
                  defaultValue={8}
                  className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                />
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Airflow (CFM)
              </label>
              <input
                type="number"
                defaultValue={1200}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Multiple objects selected. Bulk edit options:
            </div>
            <div className="space-y-2">
              <button className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                Change Material
              </button>
              <button className="w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                Delete Selected
              </button>
            </div>
          </div>
        )}

        <div className="flex space-x-2 mt-4 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
          <button className="flex-1 px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
            Apply
          </button>
          <button
            onClick={onClose}
            className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectionPopUp;
