/**
 * SizeWise Suite - New App Shell with Centered Navigation
 * 
 * Replaces the sidebar-based AppShell with centered navigation
 * Implements the layout system required by SizeWise Task V1
 * 
 * Features:
 * - Centered top navigation (no sidebar)
 * - Desktop-first design
 * - Theme system integration
 * - Feature flag controlled rollout
 */

'use client';

import React, { useEffect } from 'react';
import { CenteredNavigation } from '../../shared/components/navigation/CenteredNavigation';
import { useFeatureFlags } from '../hooks/useFeatureFlags';
import { useTheme } from '../hooks/useTheme';
import { AppErrorBoundary } from '../../shared/components/error-boundaries/AppErrorBoundary';
import { NavigationSkeleton, LoadingFallback } from '../../shared/components/loading/LoadingFallback';

// Import the old AppShell as fallback
import { AppShell as LegacyAppShell } from '../../components/ui/AppShell';

interface AppShellProps {
  children: React.ReactNode;
}

export const AppShell: React.FC<AppShellProps> = ({ children }) => {
  const { enableCenteredNavigation, enableNewThemeSystem } = useFeatureFlags();
  const { theme, setTheme } = useTheme();
  const [isLoading, setIsLoading] = React.useState(true);
  const [navigationError, setNavigationError] = React.useState(false);

  // Apply theme to document
  useEffect(() => {
    if (enableNewThemeSystem) {
      const root = document.documentElement;

      if (theme === 'dark') {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }

      // Apply theme-specific CSS variables
      if (theme === 'light') {
        root.style.setProperty('--background', '255 255 255');
        root.style.setProperty('--foreground', '0 0 0');
      } else if (theme === 'dark') {
        root.style.setProperty('--background', '0 0 0');
        root.style.setProperty('--foreground', '255 255 255');
      }
    }
  }, [theme, enableNewThemeSystem]);

  // Initialize loading state
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // Feature flag check - use legacy AppShell if new navigation is disabled
  if (!enableCenteredNavigation) {
    return (
      <AppErrorBoundary>
        <LegacyAppShell>{children}</LegacyAppShell>
      </AppErrorBoundary>
    );
  }

  return (
    <AppErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        {/* Centered Navigation with Error Boundary */}
        <AppErrorBoundary
          fallback={<NavigationSkeleton />}
          onError={() => setNavigationError(true)}
        >
          {isLoading ? <NavigationSkeleton /> : <CenteredNavigation />}
        </AppErrorBoundary>

        {/* Main Content Area */}
        <main className="flex-1">
          <AppErrorBoundary
            fallback={
              <LoadingFallback
                message="Loading application content..."
                className="min-h-screen"
              />
            }
          >
            <div className="w-full h-full">
              {children}
            </div>
          </AppErrorBoundary>
        </main>

        {/* Global overlays and modals */}
        <div id="modal-root" />
        <div id="toast-root" />

        {/* Development error indicator */}
        {process.env.NODE_ENV === 'development' && navigationError && (
          <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
            Navigation Error - Using Fallback
          </div>
        )}
      </div>
    </AppErrorBoundary>
  );
};

export default AppShell;
