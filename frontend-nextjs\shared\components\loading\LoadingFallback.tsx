/**
 * SizeWise Suite - Loading Fallback Components
 * 
 * Provides loading states and skeleton screens
 * Improves perceived performance during component loading
 */

'use client';

import React from 'react';

interface LoadingFallbackProps {
  message?: string;
  className?: string;
}

export const LoadingFallback: React.FC<LoadingFallbackProps> = ({ 
  message = 'Loading...', 
  className = '' 
}) => {
  return (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">{message}</p>
      </div>
    </div>
  );
};

export const NavigationSkeleton: React.FC = () => {
  return (
    <nav className="w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo skeleton */}
          <div className="flex-shrink-0">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-lg animate-pulse"></div>
              <div className="w-32 h-6 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            </div>
          </div>

          {/* Navigation items skeleton */}
          <div className="hidden md:flex items-center space-x-8">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="w-16 h-6 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            ))}
          </div>

          {/* Right side skeleton */}
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export const AirDuctSizerSkeleton: React.FC = () => {
  return (
    <div className="w-full h-screen bg-gray-50 dark:bg-gray-900">
      {/* Project Properties Panel Skeleton */}
      <div className="absolute top-4 left-4 w-80 h-96 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 animate-pulse">
        <div className="p-4">
          <div className="w-48 h-6 bg-gray-300 dark:bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="w-full h-4 bg-gray-300 dark:bg-gray-600 rounded"></div>
            ))}
          </div>
        </div>
      </div>

      {/* 3D Canvas Skeleton */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading 3D Workspace...</p>
        </div>
      </div>

      {/* View Cube Skeleton */}
      <div className="absolute top-4 right-4 w-20 h-20 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 animate-pulse"></div>

      {/* Drawing FAB Skeleton */}
      <div className="absolute bottom-20 right-6 w-14 h-14 bg-blue-500/80 rounded-full animate-pulse"></div>

      {/* Warning Panel Skeleton */}
      <div className="absolute top-1/2 right-0 transform -translate-y-1/2 w-80 h-64 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-l-lg border border-gray-200/50 dark:border-gray-700/50 animate-pulse"></div>

      {/* Calculation Bar Skeleton */}
      <div className="absolute bottom-0 left-0 right-0 h-16 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200/50 dark:border-gray-700/50">
        <div className="px-6 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="text-center">
                <div className="w-16 h-4 bg-gray-300 dark:bg-gray-600 rounded mb-1 animate-pulse"></div>
                <div className="w-12 h-6 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
          <div className="flex space-x-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="w-24 h-8 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Drawing Toolbar Skeleton */}
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg border border-gray-200/50 dark:border-gray-700/50 p-2">
        <div className="space-y-1">
          {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
            <div key={i} className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    </div>
  );
};

export const FeatureFlagFallback: React.FC<{ featureName: string }> = ({ featureName }) => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="text-center p-8">
        <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-700 dark:text-gray-300 mb-4">
          {featureName} Coming Soon
        </h2>
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          This feature is currently in development and will be available soon.
        </p>
        <p className="text-sm text-gray-400 dark:text-gray-500">
          Feature flag: {featureName} = disabled
        </p>
      </div>
    </div>
  );
};
