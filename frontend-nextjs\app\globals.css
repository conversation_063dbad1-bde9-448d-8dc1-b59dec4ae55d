@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #1976d2;
  --color-primary-dark: #1565c0;
  --color-primary-light: #42a5f5;
  --color-secondary: #424242;
  --color-accent: #ff9800;
  --color-success: #4caf50;
  --color-warning: #ff9800;
  --color-error: #f44336;
  --color-info: #2196f3;
  --color-highlight: #3b82f6;
  --color-background: #f5f5f5;
  --color-surface: #ffffff;
  --color-text-primary: #212121;
  --color-text-secondary: #757575;
  --color-text-disabled: #bdbdbd;
  --color-border: #e0e0e0;
  --color-grid-light: #e5e7eb;
  --color-grid-dark: #d1d5db;
  --color-label: #374151;
  --color-label-secondary: #6366f1;
  --color-positive: #059669;
}

/* Custom keyframes - moveBackground removed as no longer needed */

/* Ensure backdrop-filter works properly */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    backdrop-filter: blur(10px);
  }
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    background: rgba(255, 255, 255, 0.1);
  }
}

/* Accessibility improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .border-neutral-200 {
    border-color: var(--color-text-primary);
  }

  .dark .border-neutral-700 {
    border-color: var(--color-surface);
  }
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Ensure touch targets are at least 44px */
  button, a, input, select, textarea {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve text readability on mobile */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

/* Focus visible improvements */
.focus\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px var(--ring-color, var(--color-highlight));
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}
