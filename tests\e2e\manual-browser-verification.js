/**
 * SizeWise Suite - Manual Browser Verification
 * 
 * Uses browser automation to verify the application functionality
 * without <PERSON><PERSON>'s complex configuration
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class BrowserVerifier {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async init() {
    console.log('🚀 Starting Browser Verification...\n');
    this.browser = await chromium.launch({ headless: false });
    this.page = await this.browser.newPage();
    
    // Set up console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Console Error: ${msg.text()}`);
      }
    });
  }

  async test(description, testFn) {
    try {
      console.log(`🧪 Testing: ${description}`);
      await testFn();
      this.results.passed++;
      this.results.tests.push({ description, status: '✅ PASS' });
      console.log(`✅ PASS: ${description}\n`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ description, status: '❌ FAIL', error: error.message });
      console.log(`❌ FAIL: ${description} - ${error.message}\n`);
    }
  }

  async runAllTests() {
    await this.init();

    // Test 1: Main Application Load
    await this.test('Main application loads correctly', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Check title
      const title = await this.page.title();
      if (!title.includes('SizeWise')) {
        throw new Error(`Expected title to contain 'SizeWise', got: ${title}`);
      }
      
      // Take screenshot
      await this.page.screenshot({ path: 'test-results/main-application.png', fullPage: true });
    });

    // Test 2: Centered Navigation
    await this.test('Centered navigation is present and functional', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Check for navigation
      const nav = this.page.locator('nav');
      await nav.waitFor({ state: 'visible' });
      
      // Check for SizeWise Suite logo/text
      const logo = this.page.locator('text=SizeWise Suite');
      await logo.waitFor({ state: 'visible' });
      
      // Check for navigation items
      const navItems = ['Home', 'File', 'Projects', 'Tools', 'Profile'];
      for (const item of navItems) {
        const navItem = this.page.locator(`text=${item}`).first();
        await navItem.waitFor({ state: 'visible' });
      }
      
      await this.page.screenshot({ path: 'test-results/centered-navigation.png' });
    });

    // Test 3: Dropdown Functionality
    await this.test('Navigation dropdowns work correctly', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Test Tools dropdown
      const toolsButton = this.page.locator('button:has-text("Tools")');
      await toolsButton.click();
      
      // Check for Air Duct Sizer link
      const airDuctSizer = this.page.locator('text=Air Duct Sizer');
      await airDuctSizer.waitFor({ state: 'visible' });
      
      await this.page.screenshot({ path: 'test-results/tools-dropdown.png' });
    });

    // Test 4: Air Duct Sizer Navigation
    await this.test('Can navigate to Air Duct Sizer', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Open Tools dropdown and click Air Duct Sizer
      await this.page.click('button:has-text("Tools")');
      await this.page.click('text=Air Duct Sizer');
      
      // Wait for navigation
      await this.page.waitForURL('**/air-duct-sizer-new');
      
      await this.page.screenshot({ path: 'test-results/air-duct-sizer-navigation.png', fullPage: true });
    });

    // Test 5: Air Duct Sizer Page Load
    await this.test('Air Duct Sizer page loads without critical errors', async () => {
      await this.page.goto('http://localhost:3000/air-duct-sizer-new');
      await this.page.waitForLoadState('networkidle');
      
      // Wait for any dynamic content
      await this.page.waitForTimeout(2000);
      
      // Check URL
      const url = this.page.url();
      if (!url.includes('air-duct-sizer-new')) {
        throw new Error(`Expected URL to contain 'air-duct-sizer-new', got: ${url}`);
      }
      
      await this.page.screenshot({ path: 'test-results/air-duct-sizer-page.png', fullPage: true });
    });

    // Test 6: No Critical Console Errors
    await this.test('No critical module resolution errors', async () => {
      const errors = [];
      
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          errors.push(msg.text());
        }
      });
      
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      await this.page.waitForTimeout(3000);
      
      // Filter critical errors
      const criticalErrors = errors.filter(error => 
        error.includes('Module not found') ||
        error.includes('Cannot resolve') ||
        error.includes('@heroicons') ||
        error.includes('AppShell')
      );
      
      if (criticalErrors.length > 0) {
        throw new Error(`Critical errors found: ${criticalErrors.join(', ')}`);
      }
    });

    // Test 7: Heroicons Display
    await this.test('Heroicons display correctly in navigation', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Open a dropdown to check for icons
      await this.page.click('button:has-text("File")');
      
      // Check for SVG icons
      const svgIcons = this.page.locator('svg');
      const iconCount = await svgIcons.count();
      
      if (iconCount === 0) {
        throw new Error('No SVG icons found in navigation');
      }
      
      await this.page.screenshot({ path: 'test-results/heroicons-display.png' });
    });

    // Test 8: Responsive Design
    await this.test('Application is responsive', async () => {
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      // Test mobile viewport
      await this.page.setViewportSize({ width: 375, height: 667 });
      await this.page.waitForTimeout(1000);
      
      // Navigation should still be visible
      const nav = this.page.locator('nav');
      await nav.waitFor({ state: 'visible' });
      
      await this.page.screenshot({ path: 'test-results/mobile-responsive.png', fullPage: true });
      
      // Reset to desktop
      await this.page.setViewportSize({ width: 1920, height: 1080 });
    });

    // Test 9: Performance Check
    await this.test('Application loads within reasonable time', async () => {
      const startTime = Date.now();
      
      await this.page.goto('http://localhost:3000');
      await this.page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      console.log(`   Load time: ${loadTime}ms`);
      
      if (loadTime > 10000) {
        throw new Error(`Load time too slow: ${loadTime}ms`);
      }
    });

    await this.cleanup();
    return this.generateReport();
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  generateReport() {
    console.log('\n📊 Browser Verification Results');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        passed: this.results.passed,
        failed: this.results.failed,
        successRate: ((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)
      },
      tests: this.results.tests,
      screenshots: [
        'test-results/main-application.png',
        'test-results/centered-navigation.png',
        'test-results/tools-dropdown.png',
        'test-results/air-duct-sizer-navigation.png',
        'test-results/air-duct-sizer-page.png',
        'test-results/heroicons-display.png',
        'test-results/mobile-responsive.png'
      ]
    };
    
    // Ensure test-results directory exists
    const testResultsDir = path.join(__dirname, '../../test-results');
    if (!fs.existsSync(testResultsDir)) {
      fs.mkdirSync(testResultsDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(testResultsDir, 'browser-verification-report.json'),
      JSON.stringify(report, null, 2)
    );
    
    if (this.results.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! SizeWise Suite architecture migration is working correctly!');
    } else {
      console.log('\n⚠️  Some tests failed. Check the details above.');
    }
    
    console.log('\n📄 Detailed report saved to: test-results/browser-verification-report.json');
    console.log('📸 Screenshots saved to: test-results/');
    
    return report;
  }
}

// Run the verification
async function runVerification() {
  const verifier = new BrowserVerifier();
  try {
    await verifier.runAllTests();
  } catch (error) {
    console.error('❌ Verification failed:', error);
    await verifier.cleanup();
  }
}

runVerification();
