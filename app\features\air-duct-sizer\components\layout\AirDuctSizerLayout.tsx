/**
 * SizeWise Suite - Air Duct Sizer Layout
 * 
 * Implements the 9 required UI elements with precise placement
 * as specified in SizeWise Task V1 requirements
 * 
 * Required UI Elements:
 * 1. Project Properties Panel (top-left, retractable)
 * 2. 3D Canvas Workspace (center viewport)
 * 3. Drawing Tool FAB (bottom-right)
 * 4. View Cube (top-right)
 * 5. Calculation Bar (bottom full-width)
 * 6. Import/Export Panel (above calculation bar, collapsible)
 * 7. Warning Panel (right edge, retractable)
 * 8. Selection Pop-Up (contextual)
 * 9. Toolbar (drawing tools)
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Canvas3D } from '../workspace/Canvas3D';
import { ProjectPropertiesPanel } from '../panels/ProjectPropertiesPanel';
import { WarningPanel } from '../panels/WarningPanel';
import { CalculationBar } from '../panels/CalculationBar';
import { ImportExportPanel } from '../panels/ImportExportPanel';
import { DrawingFAB } from '../ui/DrawingFAB';
import { ViewCube } from '../ui/ViewCube';
import { SelectionPopUp } from '../ui/SelectionPopUp';
import { DrawingToolbar } from '../ui/DrawingToolbar';
import { useFeatureFlags } from '@/core/hooks/useFeatureFlags';
import { useCanvas3DStore } from '../../store/canvas-store';

interface AirDuctSizerLayoutProps {
  className?: string;
}

export const AirDuctSizerLayout: React.FC<AirDuctSizerLayoutProps> = ({ 
  className = '' 
}) => {
  const { enableNewAirDuctSizer, enable3DCanvas } = useFeatureFlags();
  
  // Panel visibility states
  const [isProjectPropertiesOpen, setIsProjectPropertiesOpen] = useState(false);
  const [isWarningPanelOpen, setIsWarningPanelOpen] = useState(false);
  const [isImportExportOpen, setIsImportExportOpen] = useState(false);
  
  // Canvas and selection state
  const { selectedObjects, canvasSize } = useCanvas3DStore();
  const [selectionPopUpPosition, setSelectionPopUpPosition] = useState<{ x: number; y: number } | null>(null);

  // Handle selection events
  const handleObjectSelect = useCallback((position: { x: number; y: number }) => {
    setSelectionPopUpPosition(position);
  }, []);

  const handleSelectionClear = useCallback(() => {
    setSelectionPopUpPosition(null);
  }, []);

  // Feature flag check
  if (!enableNewAirDuctSizer) {
    return (
      <div className={`flex items-center justify-center min-h-screen bg-gray-100 ${className}`}>
        <div className="text-center p-8">
          <h2 className="text-2xl font-bold text-gray-700 mb-4">
            New Air Duct Sizer Coming Soon
          </h2>
          <p className="text-gray-500 mb-4">
            The enhanced Air Duct Sizer with 3D workspace is currently in development.
          </p>
          <p className="text-sm text-gray-400">
            Feature flag: enableNewAirDuctSizer = false
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative w-full h-screen overflow-hidden bg-gray-50 dark:bg-gray-900 ${className}`}>
      {/* 1. Project Properties Panel - Top-left, retractable */}
      <ProjectPropertiesPanel
        isOpen={isProjectPropertiesOpen}
        onToggle={() => setIsProjectPropertiesOpen(!isProjectPropertiesOpen)}
        className="absolute top-4 left-4 z-30"
      />

      {/* 2. 3D Canvas Workspace - Center viewport */}
      <div className="absolute inset-0 flex flex-col">
        {/* Canvas container */}
        <div className="flex-1 relative">
          <Canvas3D
            width={canvasSize.width}
            height={canvasSize.height}
            className="w-full h-full"
          />
          
          {/* 4. View Cube - Top-right */}
          <ViewCube className="absolute top-4 right-4 z-20" />
          
          {/* 9. Drawing Toolbar - Left side of canvas */}
          <DrawingToolbar className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20" />
        </div>

        {/* Bottom panels container */}
        <div className="flex-shrink-0">
          {/* 6. Import/Export Panel - Above calculation bar, collapsible */}
          <ImportExportPanel
            isOpen={isImportExportOpen}
            onToggle={() => setIsImportExportOpen(!isImportExportOpen)}
          />
          
          {/* 5. Calculation Bar - Bottom full-width */}
          <CalculationBar />
        </div>
      </div>

      {/* 3. Drawing Tool FAB - Bottom-right */}
      <DrawingFAB className="absolute bottom-20 right-6 z-30" />

      {/* 7. Warning Panel - Right edge, retractable */}
      <WarningPanel
        isOpen={isWarningPanelOpen}
        onToggle={() => setIsWarningPanelOpen(!isWarningPanelOpen)}
        className="absolute top-1/2 right-0 transform -translate-y-1/2 z-20"
      />

      {/* 8. Selection Pop-Up - Contextual */}
      {selectionPopUpPosition && selectedObjects.length > 0 && (
        <SelectionPopUp
          position={selectionPopUpPosition}
          selectedObjects={selectedObjects}
          onClose={handleSelectionClear}
          className="absolute z-40"
        />
      )}

      {/* Development info overlay */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute bottom-4 left-4 bg-black/80 text-white text-xs p-2 rounded z-50">
          <div>Air Duct Sizer Layout Active</div>
          <div>3D Canvas: {enable3DCanvas ? 'Enabled' : 'Disabled'}</div>
          <div>Selected: {selectedObjects.length} objects</div>
          <div>Panels: PP({isProjectPropertiesOpen ? 'Open' : 'Closed'}), W({isWarningPanelOpen ? 'Open' : 'Closed'}), IE({isImportExportOpen ? 'Open' : 'Closed'})</div>
        </div>
      )}
    </div>
  );
};

export default AirDuctSizerLayout;
