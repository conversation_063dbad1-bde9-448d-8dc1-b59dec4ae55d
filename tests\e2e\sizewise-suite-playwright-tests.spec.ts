/**
 * SizeWise Suite - Comprehensive Playwright E2E Tests
 * 
 * Tests the newly implemented architecture migration including:
 * - Centered navigation system
 * - New Air Duct Sizer with 3D workspace
 * - Module resolution fixes
 * - Feature flag functionality
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = 'http://localhost:3000';
const AIR_DUCT_SIZER_URL = `${BASE_URL}/air-duct-sizer-new`;

test.describe('SizeWise Suite Architecture Migration Tests', () => {
  
  test.describe('1. Main Application - Centered Navigation System', () => {
    
    test('should load the main application with centered navigation', async ({ page }) => {
      await page.goto(BASE_URL);
      
      // Wait for the page to load
      await page.waitForLoadState('networkidle');
      
      // Check that the page loads without errors
      await expect(page).toHaveTitle(/SizeWise Suite/);
      
      // Verify centered navigation is present
      const navigation = page.locator('nav');
      await expect(navigation).toBeVisible();
      
      // Check for SizeWise Suite logo/brand
      const logo = page.locator('text=SizeWise Suite');
      await expect(logo).toBeVisible();
      
      // Take screenshot of main page
      await page.screenshot({ path: 'test-results/main-application-loaded.png', fullPage: true });
    });

    test('should have all 5 navigation menu items', async ({ page }) => {
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Check for all required navigation items
      const navItems = ['Home', 'File', 'Projects', 'Tools', 'Profile'];
      
      for (const item of navItems) {
        const navItem = page.locator(`text=${item}`).first();
        await expect(navItem).toBeVisible();
      }
      
      await page.screenshot({ path: 'test-results/navigation-items-visible.png' });
    });

    test('should open dropdown menus on click', async ({ page }) => {
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Test File dropdown
      const fileButton = page.locator('button:has-text("File")');
      await fileButton.click();
      
      // Check for dropdown items
      await expect(page.locator('text=New Project')).toBeVisible();
      await expect(page.locator('text=Open Project')).toBeVisible();
      await expect(page.locator('text=Import')).toBeVisible();
      
      await page.screenshot({ path: 'test-results/file-dropdown-open.png' });
      
      // Close dropdown by clicking elsewhere
      await page.click('body');
      
      // Test Tools dropdown
      const toolsButton = page.locator('button:has-text("Tools")');
      await toolsButton.click();
      
      // Check for Air Duct Sizer link
      await expect(page.locator('text=Air Duct Sizer')).toBeVisible();
      await expect(page.locator('text=Load Calculator')).toBeVisible();
      
      await page.screenshot({ path: 'test-results/tools-dropdown-open.png' });
    });

    test('should have working hover effects and glassmorphism', async ({ page }) => {
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Check navigation background has backdrop-blur (glassmorphism)
      const nav = page.locator('nav');
      const navClasses = await nav.getAttribute('class');
      expect(navClasses).toContain('backdrop-blur');
      
      // Test hover effects on navigation items
      const homeLink = page.locator('text=Home').first();
      await homeLink.hover();
      
      // Take screenshot showing hover state
      await page.screenshot({ path: 'test-results/navigation-hover-effects.png' });
    });

    test('should navigate to Air Duct Sizer from Tools menu', async ({ page }) => {
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Open Tools dropdown
      await page.click('button:has-text("Tools")');
      
      // Click on Air Duct Sizer
      await page.click('text=Air Duct Sizer');
      
      // Verify navigation to new Air Duct Sizer
      await expect(page).toHaveURL(AIR_DUCT_SIZER_URL);
      
      await page.screenshot({ path: 'test-results/navigation-to-air-duct-sizer.png', fullPage: true });
    });
  });

  test.describe('2. New Air Duct Sizer - 3D Workspace', () => {
    
    test('should load the Air Duct Sizer with all 9 UI elements', async ({ page }) => {
      await page.goto(AIR_DUCT_SIZER_URL);
      await page.waitForLoadState('networkidle');
      
      // Wait for any dynamic content to load
      await page.waitForTimeout(2000);
      
      // Check that the page loads without major errors
      await expect(page).toHaveURL(AIR_DUCT_SIZER_URL);
      
      // Take full page screenshot
      await page.screenshot({ path: 'test-results/air-duct-sizer-full-page.png', fullPage: true });
      
      // Check for presence of key UI elements (even if feature flagged)
      const pageContent = await page.content();
      
      // Look for evidence of the 9 UI elements in the DOM
      const uiElements = [
        'Project Properties',
        'Canvas3D',
        'Drawing',
        'View Cube',
        'Calculation',
        'Import',
        'Export',
        'Warning',
        'Selection'
      ];
      
      let elementsFound = 0;
      for (const element of uiElements) {
        if (pageContent.includes(element)) {
          elementsFound++;
        }
      }
      
      console.log(`Found ${elementsFound} out of ${uiElements.length} UI elements in the DOM`);
      expect(elementsFound).toBeGreaterThan(5); // At least most elements should be present
    });

    test('should display feature flag controlled content', async ({ page }) => {
      await page.goto(AIR_DUCT_SIZER_URL);
      await page.waitForLoadState('networkidle');
      
      // Check for feature flag messaging or actual content
      const pageText = await page.textContent('body');
      
      // Should either show the new Air Duct Sizer or feature flag message
      const hasNewContent = pageText?.includes('Air Duct Sizer') || 
                           pageText?.includes('3D workspace') ||
                           pageText?.includes('Coming Soon') ||
                           pageText?.includes('development');
      
      expect(hasNewContent).toBeTruthy();
      
      await page.screenshot({ path: 'test-results/air-duct-sizer-content.png', fullPage: true });
    });

    test('should not have console errors related to module resolution', async ({ page }) => {
      const consoleErrors: string[] = [];
      
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleErrors.push(msg.text());
        }
      });
      
      await page.goto(AIR_DUCT_SIZER_URL);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // Wait for any async loading
      
      // Filter out non-critical errors
      const criticalErrors = consoleErrors.filter(error => 
        error.includes('Module not found') ||
        error.includes('Cannot resolve') ||
        error.includes('@heroicons') ||
        error.includes('AppShell')
      );
      
      console.log('Console errors found:', consoleErrors);
      console.log('Critical module errors:', criticalErrors);
      
      // Should have no critical module resolution errors
      expect(criticalErrors.length).toBe(0);
    });
  });

  test.describe('3. Module Resolution and Icon Verification', () => {
    
    test('should display heroicons correctly in navigation', async ({ page }) => {
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Open a dropdown to check for chevron icons
      await page.click('button:has-text("File")');
      
      // Check for SVG icons (heroicons render as SVG)
      const svgIcons = page.locator('svg');
      const iconCount = await svgIcons.count();
      
      expect(iconCount).toBeGreaterThan(0);
      
      // Take screenshot showing icons
      await page.screenshot({ path: 'test-results/heroicons-display.png' });
    });

    test('should load without AppShell import errors', async ({ page }) => {
      const networkErrors: string[] = [];
      
      page.on('response', response => {
        if (!response.ok() && response.url().includes('AppShell')) {
          networkErrors.push(`${response.status()} ${response.url()}`);
        }
      });
      
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      expect(networkErrors.length).toBe(0);
    });
  });

  test.describe('4. Performance and Functionality', () => {
    
    test('should load pages within reasonable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      console.log(`Main page load time: ${loadTime}ms`);
      
      // Should load within 10 seconds
      expect(loadTime).toBeLessThan(10000);
      
      // Test Air Duct Sizer load time
      const adsStartTime = Date.now();
      await page.goto(AIR_DUCT_SIZER_URL);
      await page.waitForLoadState('networkidle');
      
      const adsLoadTime = Date.now() - adsStartTime;
      console.log(`Air Duct Sizer load time: ${adsLoadTime}ms`);
      
      expect(adsLoadTime).toBeLessThan(15000);
    });

    test('should be responsive and mobile-friendly', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Navigation should still be visible
      const nav = page.locator('nav');
      await expect(nav).toBeVisible();
      
      await page.screenshot({ path: 'test-results/mobile-responsive.png', fullPage: true });
      
      // Test tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      await page.screenshot({ path: 'test-results/tablet-responsive.png', fullPage: true });
      
      // Reset to desktop
      await page.setViewportSize({ width: 1920, height: 1080 });
    });

    test('should handle navigation between pages smoothly', async ({ page }) => {
      await page.goto(BASE_URL);
      await page.waitForLoadState('networkidle');
      
      // Navigate to Air Duct Sizer
      await page.click('button:has-text("Tools")');
      await page.click('text=Air Duct Sizer');
      await page.waitForLoadState('networkidle');
      
      // Navigate back to home
      await page.click('text=SizeWise Suite');
      await page.waitForLoadState('networkidle');
      
      // Should be back at home
      await expect(page).toHaveURL(BASE_URL);
      
      await page.screenshot({ path: 'test-results/navigation-flow-complete.png' });
    });
  });
});

test.describe('5. Architecture Migration Verification', () => {
  
  test('should confirm centered navigation replaces sidebar', async ({ page }) => {
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // Check that there's no sidebar element
    const sidebar = page.locator('[data-testid="sidebar"], .sidebar, #sidebar');
    await expect(sidebar).toHaveCount(0);
    
    // Confirm navigation is at the top
    const nav = page.locator('nav');
    const navBox = await nav.boundingBox();
    
    if (navBox) {
      // Navigation should be near the top of the page
      expect(navBox.y).toBeLessThan(100);
    }
    
    await page.screenshot({ path: 'test-results/no-sidebar-confirmed.png', fullPage: true });
  });

  test('should verify feature flag system is operational', async ({ page }) => {
    await page.goto(AIR_DUCT_SIZER_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for any feature flag related content or functionality
    const pageContent = await page.content();
    
    // Look for evidence of feature flag system
    const hasFeatureFlags = pageContent.includes('feature') || 
                           pageContent.includes('flag') ||
                           pageContent.includes('enable') ||
                           pageContent.includes('development');
    
    // Feature flag system should be present in some form
    expect(hasFeatureFlags).toBeTruthy();
  });
});
