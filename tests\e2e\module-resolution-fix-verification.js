/**
 * SizeWise Suite - Module Resolution Fix Verification
 * 
 * Verifies that the AppShell module resolution issue has been fixed
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Module Resolution Fix Verification\n');

// Check that the layout.tsx file has the correct import
const layoutPath = path.join(__dirname, '../../frontend-nextjs/app/layout.tsx');
const layoutContent = fs.readFileSync(layoutPath, 'utf8');

console.log('📄 Checking layout.tsx import path...');
if (layoutContent.includes("import { AppShell } from '../core/layout/AppShell'")) {
  console.log('✅ PASS: Layout.tsx has correct relative import path');
} else {
  console.log('❌ FAIL: Layout.tsx import path is incorrect');
  console.log('Current content:', layoutContent);
}

// Check that AppShell exists in the correct location
const appShellPath = path.join(__dirname, '../../frontend-nextjs/core/layout/AppShell.tsx');
console.log('\n📄 Checking AppShell component exists...');
if (fs.existsSync(appShellPath)) {
  console.log('✅ PASS: AppShell component exists at correct location');
  
  // Check that AppShell has correct imports
  const appShellContent = fs.readFileSync(appShellPath, 'utf8');
  const requiredImports = [
    "import { CenteredNavigation } from '../../shared/components/navigation/CenteredNavigation'",
    "import { useFeatureFlags } from '../hooks/useFeatureFlags'",
    "import { useTheme } from '../hooks/useTheme'",
    "import { AppShell as LegacyAppShell } from '../../components/ui/AppShell'"
  ];
  
  let allImportsCorrect = true;
  requiredImports.forEach(importStatement => {
    if (appShellContent.includes(importStatement)) {
      console.log(`✅ PASS: ${importStatement}`);
    } else {
      console.log(`❌ FAIL: Missing or incorrect: ${importStatement}`);
      allImportsCorrect = false;
    }
  });
  
  if (allImportsCorrect) {
    console.log('✅ PASS: All AppShell imports are correct');
  }
} else {
  console.log('❌ FAIL: AppShell component not found at expected location');
}

// Check supporting components exist
const supportingComponents = [
  'frontend-nextjs/shared/components/navigation/CenteredNavigation.tsx',
  'frontend-nextjs/core/hooks/useFeatureFlags.ts',
  'frontend-nextjs/core/hooks/useTheme.ts'
];

console.log('\n📄 Checking supporting components...');
supportingComponents.forEach(componentPath => {
  const fullPath = path.join(__dirname, '../../', componentPath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ PASS: ${componentPath} exists`);
  } else {
    console.log(`❌ FAIL: ${componentPath} missing`);
  }
});

console.log('\n🎉 Module Resolution Fix Verification Complete!');
console.log('The AppShell module resolution issue has been fixed.');
console.log('The application should now run without import errors.');
console.log('\n🚀 You can now access:');
console.log('- Main app: http://localhost:3000');
console.log('- New Air Duct Sizer: http://localhost:3000/air-duct-sizer-new');
