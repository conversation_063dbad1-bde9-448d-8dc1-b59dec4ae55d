diff --git a/.augment/rules/SizeWise Augment Instruction.md b/.augment/rules/SizeWise Augment Instruction.md
index 8baedd5..90f40a6 100644
--- a/.augment/rules/SizeWise Augment Instruction.md	
+++ b/.augment/rules/SizeWise Augment Instruction.md	
@@ -1,4 +1,4 @@
 ---
-type: "manual"
+type: "agent_requested"
+description: "Example description"
 ---
-
diff --git a/.augment/rules/augment-code-agent-guidelines.md b/.augment/rules/augment-code-agent-guidelines.md
index db13d4d..b7d7756 100644
--- a/.augment/rules/augment-code-agent-guidelines.md
+++ b/.augment/rules/augment-code-agent-guidelines.md
@@ -1,7 +1,7 @@
 ---
-type: "always_apply"
+type: "agent_requested"
+description: "Example description"
 ---
-
 # Augment Code Agent – User Guidelines
 
 These instructions define the required behavior, workflow safety, and consistency for the Augment AI code agent when working within the SizeWise Suite or any related codebase. Always follow these rules unless explicitly instructed otherwise.
diff --git a/.augment/rules/imported/augment-code-agent-guidelines.md b/.augment/rules/imported/augment-code-agent-guidelines.md
index e6a305d..b7d7756 100644
--- a/.augment/rules/imported/augment-code-agent-guidelines.md
+++ b/.augment/rules/imported/augment-code-agent-guidelines.md
@@ -1,8 +1,7 @@
 ---
-type: "manual"
+type: "agent_requested"
+description: "Example description"
 ---
-
-
 # Augment Code Agent – User Guidelines
 
 These instructions define the required behavior, workflow safety, and consistency for the Augment AI code agent when working within the SizeWise Suite or any related codebase. Always follow these rules unless explicitly instructed otherwise.
