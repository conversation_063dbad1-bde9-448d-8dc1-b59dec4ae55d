/**
 * SizeWise Suite - Feature Flags Hook
 * 
 * Provides access to feature flags for progressive rollout
 * Required by Augment Implementation Protocol
 */

'use client';

import { useState, useEffect } from 'react';

export interface FeatureFlags {
  // Migration flags - progressive enablement
  enableCenteredNavigation: boolean;
  enable3DCanvas: boolean;
  enableNewAirDuctSizer: boolean;
  
  // Feature flags - progressive enablement
  enableProjectProperties: boolean;
  enableStandardsCompliance: boolean;
  enableAdvancedCalculations: boolean;
  
  // UI flags
  enableGlassmorphismIntegration: boolean;
  enableNewThemeSystem: boolean;
}

// Default feature flags - enabled for development
export const defaultFeatureFlags: FeatureFlags = {
  // Migration flags - enabled for demonstration
  enableCenteredNavigation: true,
  enable3DCanvas: true,
  enableNewAirDuctSizer: true,
  
  // Feature flags - progressive enablement
  enableProjectProperties: true,
  enableStandardsCompliance: true, // Core requirement
  enableAdvancedCalculations: true,
  
  // UI flags
  enableGlassmorphismIntegration: true,
  enableNewThemeSystem: true,
};

// Feature flag overrides for development/testing
const featureFlagOverrides: Partial<FeatureFlags> = {};

export const useFeatureFlags = (): FeatureFlags & {
  setFeatureFlagOverride: (flag: keyof FeatureFlags, value: boolean) => void;
  getFeatureFlags: () => FeatureFlags;
} => {
  const [flags, setFlags] = useState<FeatureFlags>(defaultFeatureFlags);

  useEffect(() => {
    // In production, you would fetch flags from a service
    // For now, we use defaults with any overrides
    const finalFlags = { ...defaultFeatureFlags, ...featureFlagOverrides };
    setFlags(finalFlags);
  }, []);

  const setFeatureFlagOverride = (flag: keyof FeatureFlags, value: boolean) => {
    featureFlagOverrides[flag] = value;
    setFlags(prev => ({ ...prev, [flag]: value }));
  };

  const getFeatureFlags = (): FeatureFlags => {
    return flags;
  };

  return {
    ...flags,
    setFeatureFlagOverride,
    getFeatureFlags,
  };
};
