/**
 * SizeWise Suite - Calculation Bar
 * 
 * Bottom full-width persistent bar showing key calculation results
 */

'use client';

import React from 'react';

export const CalculationBar: React.FC = () => {
  const mockCalculations = [
    { label: 'Total Airflow', value: '12,500', unit: 'CFM', status: 'good' },
    { label: 'System Pressure', value: '2.8', unit: 'in. w.g.', status: 'good' },
    { label: 'Max Velocity', value: '1,850', unit: 'FPM', status: 'warning' },
    { label: 'Total Duct Area', value: '485', unit: 'sq ft', status: 'good' },
    { label: 'Material Cost', value: '$3,240', unit: '', status: 'good' },
    { label: 'Compliance', value: '98%', unit: '', status: 'good' },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '⚪';
    }
  };

  return (
    <div className="w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-t border-gray-200/50 dark:border-gray-700/50">
      <div className="px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {mockCalculations.map((calc, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span className="text-sm">{getStatusIcon(calc.status)}</span>
                <div className="text-center">
                  <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                    {calc.label}
                  </div>
                  <div className="font-semibold text-gray-800 dark:text-white">
                    {calc.value} {calc.unit}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="flex items-center space-x-3">
            <button className="px-4 py-2 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
              Recalculate
            </button>
            <button className="px-4 py-2 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors">
              Generate Report
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalculationBar;
