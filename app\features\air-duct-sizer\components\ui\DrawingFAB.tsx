/**
 * SizeWise Suite - Drawing Tool FAB (Floating Action Button)
 * 
 * Bottom-right toggle for drawing stick lines
 * Part of the 9 required UI elements for Air Duct Sizer
 */

'use client';

import React, { useState } from 'react';
import { PencilIcon, CursorArrowRaysIcon } from '@heroicons/react/24/outline';
import { useDrawingStore } from '../../store/drawing-store';

interface DrawingFABProps {
  className?: string;
}

export const DrawingFAB: React.FC<DrawingFABProps> = ({ className = '' }) => {
  const { drawingTool, setDrawingTool } = useDrawingStore();
  const [isExpanded, setIsExpanded] = useState(false);

  const tools = [
    { id: 'select', icon: CursorArrowRaysIcon, label: 'Select', color: 'bg-gray-500' },
    { id: 'stick-line', icon: PencilIcon, label: 'Draw Stick Line', color: 'bg-blue-500' },
  ];

  const activeTool = tools.find(tool => tool.id === drawingTool) || tools[0];

  return (
    <div className={`${className}`}>
      {/* Expanded Tools */}
      {isExpanded && (
        <div className="mb-3 space-y-2">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => {
                setDrawingTool(tool.id as any);
                setIsExpanded(false);
              }}
              className={`w-12 h-12 rounded-full ${tool.color} text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center ${
                drawingTool === tool.id ? 'ring-4 ring-white/50' : ''
              }`}
              title={tool.label}
            >
              <tool.icon className="w-6 h-6" />
            </button>
          ))}
        </div>
      )}

      {/* Main FAB */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-14 h-14 rounded-full ${activeTool.color} text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center ${
          isExpanded ? 'rotate-45' : ''
        }`}
        title={isExpanded ? 'Close' : activeTool.label}
      >
        {isExpanded ? (
          <div className="w-6 h-0.5 bg-white"></div>
        ) : (
          <activeTool.icon className="w-7 h-7" />
        )}
      </button>

      {/* Tool Label */}
      {!isExpanded && (
        <div className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
          {activeTool.label}
        </div>
      )}
    </div>
  );
};
