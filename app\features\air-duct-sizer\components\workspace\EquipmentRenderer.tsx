/**
 * SizeWise Suite - Equipment Renderer
 * 
 * Renders HVAC equipment in 3D space
 * Part of the 3D-first implementation
 */

'use client';

import React from 'react';
import * as THREE from 'three';
import type { Equipment3D } from '../../store/canvas-store';

interface EquipmentRendererProps {
  equipment: Equipment3D[];
  selectedObjects: string[];
}

export const EquipmentRenderer: React.FC<EquipmentRendererProps> = ({
  equipment,
  selectedObjects,
}) => {
  const renderEquipment = (eq: Equipment3D) => {
    const isSelected = selectedObjects.includes(eq.id);
    const color = isSelected ? '#ff6b6b' : '#4ade80';
    
    // Basic equipment representations
    switch (eq.type) {
      case 'fan':
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <cylinderGeometry args={[0.5, 0.5, 0.3, 8]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <cylinderGeometry args={[0.5, 0.5, 0.3, 8]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
      
      case 'damper':
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <boxGeometry args={[0.8, 0.1, 0.6]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <boxGeometry args={[0.8, 0.1, 0.6]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
      
      case 'diffuser':
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <coneGeometry args={[0.4, 0.2, 8]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <coneGeometry args={[0.4, 0.2, 8]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
      
      case 'grille':
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <boxGeometry args={[0.6, 0.05, 0.4]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <boxGeometry args={[0.6, 0.05, 0.4]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
      
      case 'coil':
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <boxGeometry args={[1.0, 0.3, 0.8]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <boxGeometry args={[1.0, 0.3, 0.8]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
      
      case 'filter':
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <boxGeometry args={[0.8, 0.1, 0.8]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <boxGeometry args={[0.8, 0.1, 0.8]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
      
      default:
        return (
          <mesh
            key={eq.id}
            position={[eq.position.x, eq.position.y, eq.position.z]}
            rotation={[eq.rotation.x, eq.rotation.y, eq.rotation.z]}
            scale={[eq.scale.x, eq.scale.y, eq.scale.z]}
          >
            <sphereGeometry args={[0.3, 8, 6]} />
            <meshStandardMaterial color={color} />
            {isSelected && (
              <mesh>
                <sphereGeometry args={[0.3, 8, 6]} />
                <meshBasicMaterial color="#ff6b6b" wireframe />
              </mesh>
            )}
          </mesh>
        );
    }
  };

  return (
    <group>
      {equipment.map(renderEquipment)}
    </group>
  );
};
