/**
 * SizeWise Suite - Drawing Toolbar
 * 
 * Left-side toolbar for drawing tools and object manipulation
 * Part of the 9 required UI elements for Air Duct Sizer
 */

'use client';

import React from 'react';
import { 
  CursorArrowRaysIcon,
  PencilIcon,
  Square3Stack3DIcon,
  CircleStackIcon,
  CogIcon,
  RulerIcon,
  HandRaisedIcon,
  MagnifyingGlassPlusIcon,
} from '@heroicons/react/24/outline';
import { useDrawingStore } from '../../store/drawing-store';

interface DrawingToolbarProps {
  className?: string;
}

interface Tool {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  shortcut?: string;
}

const tools: Tool[] = [
  { id: 'select', icon: CursorArrowRaysIcon, label: 'Select', shortcut: 'V' },
  { id: 'stick-line', icon: PencilIcon, label: 'Draw Stick Line', shortcut: 'L' },
  { id: 'rectangle-duct', icon: Square3Stack3DIcon, label: 'Rectangle Duct', shortcut: 'R' },
  { id: 'round-duct', icon: CircleStackIcon, label: 'Round Duct', shortcut: 'C' },
  { id: 'equipment', icon: CogIcon, label: 'Equipment', shortcut: 'E' },
  { id: 'measure', icon: RulerIcon, label: 'Measure', shortcut: 'M' },
  { id: 'pan', icon: HandRaisedIcon, label: 'Pan', shortcut: 'H' },
  { id: 'zoom', icon: MagnifyingGlassPlusIcon, label: 'Zoom', shortcut: 'Z' },
];

export const DrawingToolbar: React.FC<DrawingToolbarProps> = ({ className = '' }) => {
  const { drawingTool, setDrawingTool } = useDrawingStore();

  return (
    <div className={`${className}`}>
      <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-2">
        <div className="space-y-1">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => setDrawingTool(tool.id as any)}
              className={`w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-200 group relative ${
                drawingTool === tool.id
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
              title={`${tool.label} (${tool.shortcut})`}
            >
              <tool.icon className="w-5 h-5" />
              
              {/* Tooltip */}
              <div className="absolute left-12 top-1/2 transform -translate-y-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-50">
                {tool.label}
                {tool.shortcut && (
                  <span className="ml-2 text-gray-300">({tool.shortcut})</span>
                )}
              </div>
            </button>
          ))}
        </div>
        
        {/* Tool Settings */}
        <div className="mt-3 pt-3 border-t border-gray-200/50 dark:border-gray-700/50">
          <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">
            Settings
          </div>
          
          {drawingTool === 'stick-line' && (
            <div className="space-y-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Type</label>
                <select className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50">
                  <option>Supply</option>
                  <option>Return</option>
                  <option>Exhaust</option>
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">CFM</label>
                <input
                  type="number"
                  className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                  placeholder="1000"
                />
              </div>
            </div>
          )}
          
          {(drawingTool === 'rectangle-duct' || drawingTool === 'round-duct') && (
            <div className="space-y-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Material</label>
                <select className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50">
                  <option>Galvanized Steel</option>
                  <option>Stainless Steel</option>
                  <option>Aluminum</option>
                  <option>Fiberglass</option>
                </select>
              </div>
              {drawingTool === 'rectangle-duct' && (
                <div className="grid grid-cols-2 gap-1">
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">W</label>
                    <input
                      type="number"
                      className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                      placeholder="12"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 mb-1">H</label>
                    <input
                      type="number"
                      className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                      placeholder="8"
                    />
                  </div>
                </div>
              )}
              {drawingTool === 'round-duct' && (
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Diameter</label>
                  <input
                    type="number"
                    className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50"
                    placeholder="10"
                  />
                </div>
              )}
            </div>
          )}
          
          {drawingTool === 'equipment' && (
            <div className="space-y-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Type</label>
                <select className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50">
                  <option>Fan</option>
                  <option>Damper</option>
                  <option>Diffuser</option>
                  <option>Grille</option>
                  <option>Coil</option>
                  <option>Filter</option>
                </select>
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Size</label>
                <select className="w-full text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white/50 dark:bg-gray-800/50">
                  <option>Small</option>
                  <option>Medium</option>
                  <option>Large</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
