#!/usr/bin/env node
/**
 * SizeWise Suite - Directory Structure Creation Script
 * 
 * Creates the approved feature-based directory structure
 * Follows Augment Implementation Protocol requirements
 * 
 * SAFETY: This script is idempotent and safe to run multiple times
 */

const fs = require('fs');
const path = require('path');

const DIRECTORIES = [
  // Core infrastructure
  'app/core/layout',
  'app/core/state',
  'app/core/services/api',
  'app/core/services/standards',
  'app/core/services/calculations',
  'app/core/types',

  // Features (tool-specific)
  'app/features/dashboard/components',
  'app/features/dashboard/hooks',
  'app/features/dashboard/store',
  
  'app/features/air-duct-sizer/components/workspace',
  'app/features/air-duct-sizer/components/panels',
  'app/features/air-duct-sizer/components/ui',
  'app/features/air-duct-sizer/components/layout',
  'app/features/air-duct-sizer/hooks',
  'app/features/air-duct-sizer/services/calculations',
  'app/features/air-duct-sizer/services/standards',
  'app/features/air-duct-sizer/services/export',
  'app/features/air-duct-sizer/store',
  'app/features/air-duct-sizer/types',

  'app/features/project-management/components',
  'app/features/project-management/hooks',
  'app/features/project-management/store',

  'app/features/standards-compliance/services',
  'app/features/standards-compliance/data',
  'app/features/standards-compliance/types',

  // Shared components and utilities
  'app/shared/components/panels',
  'app/shared/components/canvas',
  'app/shared/components/navigation',
  'app/shared/components/glassmorphism',
  'app/shared/hooks',
  'app/shared/utils',
  'app/shared/constants',

  // Configuration and feature flags
  'app/config',
  
  // Documentation updates
  'docs/architecture/decisions',
  'docs/guides/migration',
  
  // Testing
  'tests/migration',
  'tests/e2e/air-duct-sizer',
];

function createDirectoryStructure() {
  console.log('🏗️  Creating SizeWise Suite directory structure...');
  
  let created = 0;
  let existed = 0;

  DIRECTORIES.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      console.log(`✅ Created: ${dir}`);
      created++;
    } else {
      console.log(`⏭️  Exists: ${dir}`);
      existed++;
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`   Created: ${created} directories`);
  console.log(`   Existed: ${existed} directories`);
  console.log(`   Total: ${DIRECTORIES.length} directories`);
  
  // Create placeholder files to preserve directory structure in Git
  createPlaceholderFiles();
}

function createPlaceholderFiles() {
  console.log('\n📝 Creating placeholder files...');
  
  const placeholders = [
    'app/core/layout/.gitkeep',
    'app/features/air-duct-sizer/components/workspace/.gitkeep',
    'app/features/standards-compliance/services/.gitkeep',
    'app/shared/components/panels/.gitkeep',
    'tests/migration/.gitkeep',
  ];

  placeholders.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    if (!fs.existsSync(fullPath)) {
      fs.writeFileSync(fullPath, '# Placeholder file to preserve directory structure\n');
      console.log(`✅ Created placeholder: ${file}`);
    }
  });
}

// Feature flag configuration
function createFeatureFlags() {
  const featureFlagsPath = path.join(process.cwd(), 'app/config/feature-flags.ts');
  
  if (!fs.existsSync(featureFlagsPath)) {
    const featureFlagsContent = `/**
 * SizeWise Suite Feature Flags
 * 
 * Controls progressive rollout of new features during migration
 * All major changes must be behind feature flags per Protocol
 */

export interface FeatureFlags {
  // Migration flags
  enableCenteredNavigation: boolean;
  enable3DCanvas: boolean;
  enableNewAirDuctSizer: boolean;
  
  // Feature flags
  enableProjectProperties: boolean;
  enableStandardsCompliance: boolean;
  enableAdvancedCalculations: boolean;
  
  // UI flags
  enableGlassmorphismIntegration: boolean;
  enableNewThemeSystem: boolean;
}

export const defaultFeatureFlags: FeatureFlags = {
  // Migration flags - start disabled
  enableCenteredNavigation: false,
  enable3DCanvas: false,
  enableNewAirDuctSizer: false,
  
  // Feature flags - progressive enablement
  enableProjectProperties: false,
  enableStandardsCompliance: true, // Core requirement
  enableAdvancedCalculations: false,
  
  // UI flags
  enableGlassmorphismIntegration: false,
  enableNewThemeSystem: false,
};

// Environment-based overrides
export const getFeatureFlags = (): FeatureFlags => {
  const env = process.env.NODE_ENV;
  
  if (env === 'development') {
    return {
      ...defaultFeatureFlags,
      // Enable all features in development
      enableCenteredNavigation: true,
      enable3DCanvas: true,
      enableNewAirDuctSizer: true,
      enableProjectProperties: true,
      enableAdvancedCalculations: true,
      enableGlassmorphismIntegration: true,
      enableNewThemeSystem: true,
    };
  }
  
  return defaultFeatureFlags;
};
`;

    fs.writeFileSync(featureFlagsPath, featureFlagsContent);
    console.log('✅ Created feature flags configuration');
  }
}

// Main execution
if (require.main === module) {
  try {
    createDirectoryStructure();
    createFeatureFlags();
    console.log('\n🎉 Directory structure creation completed successfully!');
    console.log('\n⚠️  Next steps:');
    console.log('   1. Review created structure');
    console.log('   2. Run backup script before migration');
    console.log('   3. Begin Phase 1 migration');
  } catch (error) {
    console.error('❌ Error creating directory structure:', error);
    process.exit(1);
  }
}

module.exports = { createDirectoryStructure, createPlaceholderFiles };
