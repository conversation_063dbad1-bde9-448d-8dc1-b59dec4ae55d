/**
 * SizeWise Suite - Client-Only Canvas3D Wrapper
 * 
 * Prevents hydration mismatches by only rendering on client
 */

'use client';

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import Canvas3D with no SSR
const Canvas3D = dynamic(() => import('./Canvas3D').then(mod => ({ default: mod.Canvas3D })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Loading 3D workspace...</p>
      </div>
    </div>
  )
});

interface ClientOnlyCanvas3DProps {
  className?: string;
}

export const ClientOnlyCanvas3D: React.FC<ClientOnlyCanvas3DProps> = ({ className = '' }) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <div className={`w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 ${className}`}>
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Initializing 3D workspace...</p>
        </div>
      </div>
    );
  }

  return <Canvas3D className={className} />;
};

export default ClientOnlyCanvas3D;
