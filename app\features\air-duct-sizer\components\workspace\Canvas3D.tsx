/**
 * SizeWise Suite - 3D Canvas Workspace
 * 
 * 3D-first implementation for Air Duct Sizer
 * Required by Augment Implementation Protocol and SizeWise Task V1
 * 
 * Features:
 * - Stick line drawing → 3D duct conversion
 * - Three.js + React Three Fiber integration
 * - Professional HVAC tool interface
 */

'use client';

import React, { useRef, useState, useCallback, useEffect } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { 
  OrbitControls, 
  Grid, 
  GizmoHelper, 
  GizmoViewport,
  Environment,
  ContactShadows
} from '@react-three/drei';
import * as THREE from 'three';
import { useFeatureFlags } from '@/core/hooks/useFeatureFlags';
import { useCanvas3DStore } from '../../store/canvas-store';
import { useDrawingStore } from '../../store/drawing-store';
import { StickLineRenderer } from './StickLineRenderer';
import { DuctRenderer } from './DuctRenderer';
import { EquipmentRenderer } from './EquipmentRenderer';
import { DrawingTools } from './DrawingTools';

interface Canvas3DProps {
  width: number;
  height: number;
  className?: string;
}

// 3D Scene setup component
const Scene: React.FC = () => {
  const { camera, gl } = useThree();
  const controlsRef = useRef<any>();
  
  const {
    stickLines,
    ducts,
    equipment,
    selectedObjects,
    viewMode,
    gridVisible,
    snapToGrid,
  } = useCanvas3DStore();

  const {
    drawingTool,
    isDrawing,
    currentStickLine,
  } = useDrawingStore();

  // Configure camera for HVAC workspace
  useEffect(() => {
    if (camera) {
      camera.position.set(10, 10, 10);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Handle drawing interactions
  const handlePointerDown = useCallback((event: THREE.Event) => {
    if (drawingTool === 'stick-line') {
      const point = event.point;
      useDrawingStore.getState().startStickLine(point);
    }
  }, [drawingTool]);

  const handlePointerMove = useCallback((event: THREE.Event) => {
    if (isDrawing && drawingTool === 'stick-line') {
      const point = event.point;
      useDrawingStore.getState().updateStickLine(point);
    }
  }, [isDrawing, drawingTool]);

  const handlePointerUp = useCallback(() => {
    if (isDrawing && drawingTool === 'stick-line') {
      useDrawingStore.getState().finishStickLine();
    }
  }, [isDrawing, drawingTool]);

  return (
    <>
      {/* Lighting setup for professional visualization */}
      <ambientLight intensity={0.4} />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={0.8}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -5]} intensity={0.3} />

      {/* Environment and atmosphere */}
      <Environment preset="studio" />
      <fog attach="fog" args={['#f0f0f0', 50, 200]} />

      {/* Grid system for precision drawing */}
      {gridVisible && (
        <Grid
          args={[100, 100]}
          cellSize={1}
          cellThickness={0.5}
          cellColor="#e0e0e0"
          sectionSize={10}
          sectionThickness={1}
          sectionColor="#c0c0c0"
          fadeDistance={50}
          fadeStrength={1}
          followCamera={false}
          infiniteGrid={true}
        />
      )}

      {/* Interactive plane for drawing */}
      <mesh
        rotation={[-Math.PI / 2, 0, 0]}
        position={[0, 0, 0]}
        onPointerDown={handlePointerDown}
        onPointerMove={handlePointerMove}
        onPointerUp={handlePointerUp}
      >
        <planeGeometry args={[200, 200]} />
        <meshBasicMaterial 
          transparent 
          opacity={0} 
          side={THREE.DoubleSide}
        />
      </mesh>

      {/* Render stick lines */}
      <StickLineRenderer 
        stickLines={stickLines}
        currentStickLine={currentStickLine}
        selectedObjects={selectedObjects}
      />

      {/* Render 3D ducts */}
      <DuctRenderer 
        ducts={ducts}
        selectedObjects={selectedObjects}
        viewMode={viewMode}
      />

      {/* Render equipment */}
      <EquipmentRenderer 
        equipment={equipment}
        selectedObjects={selectedObjects}
      />

      {/* Ground plane with contact shadows */}
      <ContactShadows
        position={[0, -0.1, 0]}
        opacity={0.3}
        scale={100}
        blur={2}
        far={20}
      />

      {/* Camera controls */}
      <OrbitControls
        ref={controlsRef}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={5}
        maxDistance={100}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
        target={[0, 0, 0]}
      />

      {/* 3D Gizmo for orientation */}
      <GizmoHelper alignment="bottom-right" margin={[80, 80]}>
        <GizmoViewport 
          axisColors={['#ff4444', '#44ff44', '#4444ff']}
          labelColor="black"
        />
      </GizmoHelper>
    </>
  );
};

// Performance monitoring component
const PerformanceMonitor: React.FC = () => {
  const { gl } = useThree();
  const [fps, setFps] = useState(60);
  const [drawCalls, setDrawCalls] = useState(0);
  
  useFrame(() => {
    // Monitor performance metrics
    const info = gl.info;
    setDrawCalls(info.render.calls);
    
    // Simple FPS calculation
    const now = performance.now();
    if (now - (PerformanceMonitor as any).lastTime > 1000) {
      setFps(Math.round(1000 / (now - (PerformanceMonitor as any).frameTime)));
      (PerformanceMonitor as any).lastTime = now;
    }
    (PerformanceMonitor as any).frameTime = now;
  });

  // Log performance warnings
  useEffect(() => {
    if (fps < 30) {
      console.warn('Canvas3D: Low FPS detected:', fps);
    }
    if (drawCalls > 100) {
      console.warn('Canvas3D: High draw calls:', drawCalls);
    }
  }, [fps, drawCalls]);

  return null;
};

// Main Canvas3D component
export const Canvas3D: React.FC<Canvas3DProps> = ({ 
  width, 
  height, 
  className = '' 
}) => {
  const { enable3DCanvas } = useFeatureFlags();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  // Feature flag check
  if (!enable3DCanvas) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 ${className}`}
        style={{ width, height }}
      >
        <div className="text-center p-8">
          <h3 className="text-lg font-semibold text-gray-700 mb-2">
            3D Canvas Coming Soon
          </h3>
          <p className="text-gray-500">
            3D workspace is currently in development.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`relative ${className}`}
      style={{ width, height }}
    >
      <Canvas
        ref={canvasRef}
        camera={{ 
          fov: 60, 
          near: 0.1, 
          far: 1000,
          position: [10, 10, 10]
        }}
        shadows
        gl={{ 
          antialias: true,
          alpha: false,
          powerPreference: "high-performance"
        }}
        style={{ 
          width: '100%', 
          height: '100%',
          background: 'linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%)'
        }}
        onCreated={({ gl }) => {
          // Configure renderer for professional quality
          gl.shadowMap.enabled = true;
          gl.shadowMap.type = THREE.PCFSoftShadowMap;
          gl.toneMapping = THREE.ACESFilmicToneMapping;
          gl.toneMappingExposure = 1.2;
        }}
      >
        <Scene />
        <PerformanceMonitor />
      </Canvas>

      {/* Drawing tools overlay */}
      <DrawingTools />

      {/* Canvas info overlay */}
      <div className="absolute top-4 left-4 bg-white/80 backdrop-blur-sm rounded-lg p-3 text-sm">
        <div className="font-medium text-gray-700">3D Workspace</div>
        <div className="text-gray-500">
          {useCanvas3DStore.getState().stickLines.length} stick lines, {' '}
          {useCanvas3DStore.getState().ducts.length} ducts
        </div>
      </div>

      {/* Performance indicator */}
      <div className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm rounded-lg p-2 text-xs text-gray-500">
        WebGL Enabled
      </div>
    </div>
  );
};

// Export additional utilities
export { Scene };
export type { Canvas3DProps };
