/**
 * SizeWise Suite - Centered Top Navigation
 * 
 * Replaces sidebar-based navigation with centered top navigation
 * Required by SizeWise Task V1 specifications
 * 
 * Features:
 * - Centered navigation with dropdown menus
 * - Glassmorphism effects with dock-style hover
 * - Desktop-first design (no mobile hamburger)
 * - Professional HVAC tool interface
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { GlassCard } from '../glassmorphism/GlassCard';
import { useFeatureFlags } from '@/core/hooks/useFeatureFlags';

interface NavItem {
  label: string;
  href?: string;
  dropdown?: DropdownItem[];
}

interface DropdownItem {
  label: string;
  href: string;
  description?: string;
  icon?: React.ReactNode;
}

const navigationItems: NavItem[] = [
  {
    label: 'Home',
    href: '/',
  },
  {
    label: 'File',
    dropdown: [
      { label: 'New Project', href: '/projects/new', description: 'Start a new HVAC project' },
      { label: 'Open Project', href: '/projects', description: 'Open existing project' },
      { label: 'Import Plan', href: '/import', description: 'Import CAD or PDF plans' },
      { label: 'Export Report', href: '/export', description: 'Export project report' },
    ],
  },
  {
    label: 'Projects',
    dropdown: [
      { label: 'All Projects', href: '/projects', description: 'View all projects' },
      { label: 'Recent Projects', href: '/projects/recent', description: 'Recently accessed projects' },
      { label: 'Shared Projects', href: '/projects/shared', description: 'Projects shared with team' },
      { label: 'Templates', href: '/projects/templates', description: 'Project templates' },
    ],
  },
  {
    label: 'Tools',
    dropdown: [
      { label: 'Air Duct Sizer', href: '/air-duct-sizer', description: 'Size air distribution ducts' },
      { label: 'Grease Duct Sizer', href: '/grease-duct-sizer', description: 'Size kitchen exhaust ducts' },
      { label: 'Combustion Vent', href: '/combustion-vent', description: 'Size combustion venting' },
      { label: 'Estimating', href: '/estimating', description: 'Project cost estimation' },
    ],
  },
  {
    label: 'Profile',
    dropdown: [
      { label: 'Account Settings', href: '/profile/settings', description: 'Manage your account' },
      { label: 'Preferences', href: '/profile/preferences', description: 'App preferences' },
      { label: 'Team Management', href: '/profile/team', description: 'Manage team members' },
      { label: 'Sign Out', href: '/auth/signout', description: 'Sign out of your account' },
    ],
  },
];

interface DropdownMenuProps {
  items: DropdownItem[];
  isOpen: boolean;
  onClose: () => void;
}

const DropdownMenu: React.FC<DropdownMenuProps> = ({ items, isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50">
      <GlassCard className="min-w-[280px] p-2 shadow-xl">
        <div className="space-y-1">
          {items.map((item, index) => (
            <Link
              key={index}
              href={item.href}
              onClick={onClose}
              className="block px-4 py-3 rounded-lg hover:bg-white/10 transition-colors group"
            >
              <div className="flex items-start space-x-3">
                {item.icon && (
                  <div className="flex-shrink-0 mt-0.5 text-blue-500">
                    {item.icon}
                  </div>
                )}
                <div>
                  <div className="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                    {item.label}
                  </div>
                  {item.description && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {item.description}
                    </div>
                  )}
                </div>
              </div>
            </Link>
          ))}
        </div>
      </GlassCard>
    </div>
  );
};

interface NavItemComponentProps {
  item: NavItem;
  isActive: boolean;
}

const NavItemComponent: React.FC<NavItemComponentProps> = ({ item, isActive }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsHovered(true);
    if (item.dropdown) {
      setIsDropdownOpen(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    timeoutRef.current = setTimeout(() => {
      setIsDropdownOpen(false);
    }, 150);
  };

  const handleDropdownMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  const handleDropdownMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsDropdownOpen(false);
    }, 150);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const baseClasses = `
    relative px-6 py-3 rounded-xl font-medium transition-all duration-200
    flex items-center space-x-2 cursor-pointer
    ${isActive 
      ? 'bg-blue-500/20 text-blue-600 dark:text-blue-400' 
      : 'text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400'
    }
    ${isHovered ? 'bg-white/10 backdrop-blur-sm' : ''}
  `;

  if (item.href && !item.dropdown) {
    return (
      <Link
        href={item.href}
        className={baseClasses}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <span>{item.label}</span>
      </Link>
    );
  }

  return (
    <div
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={baseClasses}>
        <span>{item.label}</span>
        {item.dropdown && (
          <ChevronDownIcon 
            className={`w-4 h-4 transition-transform duration-200 ${
              isDropdownOpen ? 'rotate-180' : ''
            }`} 
          />
        )}
      </div>
      
      {item.dropdown && (
        <div
          onMouseEnter={handleDropdownMouseEnter}
          onMouseLeave={handleDropdownMouseLeave}
        >
          <DropdownMenu
            items={item.dropdown}
            isOpen={isDropdownOpen}
            onClose={() => setIsDropdownOpen(false)}
          />
        </div>
      )}
    </div>
  );
};

export const CenteredNavigation: React.FC = () => {
  const pathname = usePathname();
  const { enableCenteredNavigation } = useFeatureFlags();

  // Feature flag check
  if (!enableCenteredNavigation) {
    return null;
  }

  return (
    <nav className="w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">SW</span>
              </div>
              <span className="font-bold text-xl text-gray-900 dark:text-white">
                SizeWise Suite
              </span>
            </Link>
          </div>

          {/* Centered Navigation */}
          <div className="flex items-center space-x-1">
            {navigationItems.map((item, index) => {
              const isActive = item.href === pathname || 
                (item.dropdown && item.dropdown.some(dropdownItem => dropdownItem.href === pathname));
              
              return (
                <NavItemComponent
                  key={index}
                  item={item}
                  isActive={isActive}
                />
              );
            })}
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* Theme toggle would go here */}
            <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700"></div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default CenteredNavigation;
