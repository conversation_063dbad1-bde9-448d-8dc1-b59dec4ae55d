/**
 * SizeWise Suite - Drawing Tools Overlay
 * 
 * Overlay component for drawing tools in the 3D canvas
 * Provides visual feedback for drawing operations
 */

'use client';

import React from 'react';

interface DrawingToolsProps {
  drawingTool?: string;
  isDrawing?: boolean;
  currentStickLine?: any[];
}

export const DrawingTools: React.FC<DrawingToolsProps> = ({ 
  drawingTool = 'select',
  isDrawing = false,
  currentStickLine = null
}) => {
  if (!isDrawing) return null;

  return (
    <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-black/80 text-white px-4 py-2 rounded-lg text-sm z-10">
      {drawingTool === 'stick-line' && (
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          <span>
            Drawing stick line... 
            {currentStickLine && ` (${currentStickLine.length} points)`}
          </span>
          <span className="text-gray-300">Click to add points, Enter to finish</span>
        </div>
      )}
      
      {drawingTool === 'rectangle-duct' && (
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          <span>Drawing rectangular duct...</span>
          <span className="text-gray-300">Click and drag to create</span>
        </div>
      )}
      
      {drawingTool === 'round-duct' && (
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          <span>Drawing round duct...</span>
          <span className="text-gray-300">Click and drag to create</span>
        </div>
      )}
    </div>
  );
};

export default DrawingTools;
